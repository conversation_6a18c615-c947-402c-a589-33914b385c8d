import socket
import pickle
import json
import struct
import threading
import time
from PIL import Image, ImageTk 
import os
import sys
import logging as std_logging  
import tkinter as tk
from tkinter.scrolledtext import ScrolledText

from server_utils.vision_localizer import VisionLocalizer
from server_utils.logger_config import logger as logging  


def get_base_dir():
    """获取程序运行的基准目录（兼容开发模式和打包模式）"""
    if getattr(sys, 'frozen', False):
        return os.path.dirname(sys.executable)
    else:
        return os.path.dirname(os.path.abspath(__file__))
       
shutdown_event = threading.Event()
server_socket_global = None 

# ------------------ 服务端相关函数 ------------------

def read_server_config(config_file, part_id):
    logging.info(f"Reading server config from: {config_file}")
    with open(config_file, "r") as f:
        config = json.load(f)

    base_dir = get_base_dir()
    ip = config["server_config"]["ip"]
    port = config["server_config"]["port"]
    camera_config_path = os.path.join(base_dir, config["localizer_config"]["camera_config_path"])

    tube_config_path_dict = config["localizer_config"]["tube_configs"]
    tube_config_info = tube_config_path_dict.get(str(part_id))
    tube_config_path = os.path.join(base_dir, tube_config_info["tube_config_path"])

    img_save_time = config["save_days"]["days_to_keep"]
    img_save_location = os.path.join(base_dir, config["save_days"]["image_folder_path"])

    logging.info(f"Server config details: IP:{ip}, Port:{port}, Camera Config:{camera_config_path}, Tube Config:{tube_config_path}")
    return ip, port, camera_config_path, tube_config_path, img_save_time, img_save_location

def handle_command_camera(command, localizer):
    try:
        logging.info("Checking camera status...")
        cap_success = localizer.camera_is_connected()
        logging.debug(f"Camera status: {cap_success}")
        return struct.pack("!i", cap_success)
    except Exception as e:
        logging.error(f"Error in handle_command_camera: {e}")
        return struct.pack("!i", -1)

def client_handler(client_socket, address, localizer):
    try:
        while True:
            command = client_socket.recv(1024).decode("utf-8").strip()
            if not command:
                break
            if command.startswith("$") and "&" in command:
                command_body = command[1:]
                command_parts = command_body.split("&")
                if len(command_parts) == 2:
                    command_id = command_parts[0]
                    try:
                         if command_parts[1].isdigit():
                             tube_diameter = int(command_parts[1])
                         elif "." in command_parts[1] and command_parts[1].replace(".", "").isdigit():
                             tube_diameter = float(command_parts[1])
                         else:
                             logging.info(f"value:{command_parts[1]}")
                             tube_diameter = None
                             response = struct.pack("!i", -1)
                             client_socket.sendall(response)
                    except ValueError:
                        logging.info(f"value:{command_parts[1]}")
                        tube_diameter = None
                        response = struct.pack("!i", -1)
                        client_socket.sendall(response)
                        continue

                if command_id in ["1", "2", "3"]:
                    try:
                        part_id = str(tube_diameter)
                        config_file_path = os.path.join(os.path.dirname(__file__), "configs/config.json")
                        ip, port, camera_config_path, tube_config_path, img_save_time, img_save_location = read_server_config(
                            config_file_path, part_id)
                        localizer.update_tube_config(tube_config_path, camera_config_path)
                        logging.info(f"Tube config updated to: {tube_config_path}")
                        # localizer.cap.start()
                        logging.info("Camera thread started.")
                    except ValueError as e:
                        logging.error(f"Invalid part_id: {e}")
                        response = struct.pack("!i", -2)
                        client_socket.sendall(response)
                        continue
                    logging.info("Start handling camera command ...")
                    if command_id == "1":
                        response = handle_command_camera(command, localizer)
                    elif command_id == "2":
                        response = handle_command_localizer(command, localizer, save = False)
                    elif command_id == "3":
                        response = handle_command_localizer(command, localizer, save = True)
                    if localizer.cap.camera_is_connected() == 0:
                        localizer.cap.release_camera()
                else:
                    logging.warning(f"Invalid command format: {command_body}")
                    response = struct.pack("!i", -1)
                client_socket.sendall(response)
    except Exception as e:
        logging.error(f"Error in client_handler: {e}")
    finally:
        client_socket.close()
        logging.info(f"Client disconnected: {address}")


def delete_old_images(img_save_time, img_save_location):
    current_time = time.time()
    for filename in os.listdir(img_save_location):
        file_path = os.path.join(img_save_location, filename)
        if os.path.isfile(file_path) and filename.lower().endswith((".jpg", ".jpeg", ".png", ".bmp", ".gif")):
            file_mtime = os.path.getmtime(file_path)
            file_age_days = (current_time - file_mtime) / (60 * 60 * 24)
            if file_age_days > img_save_time:
                logging.info(f"Deleting file: {file_path} (Age: {int(file_age_days)} days)")
                os.remove(file_path)

def delete_images_periodically(img_save_time, img_save_location, interval=86400):
    if shutdown_event.is_set():
        return
    delete_old_images(img_save_time, img_save_location)
    threading.Timer(interval, delete_images_periodically, args=(img_save_time, img_save_location, interval)).start()
    
    
def server_main():
    global server_socket_global
    logging.info("Starting server...")
    config_file_path = os.path.join(get_base_dir(), "configs", "config.json")
    ip, port, camera_config_path, tube_config_path, img_save_time, img_save_location = read_server_config(config_file_path, part_id="6")
    logging.info("Initializing localizer...")
    picture_save_path = os.path.join(get_base_dir(), "saved_pictures") 
    logging.info("saved_pictures.....")
    #os.makedirs(picture_save_path, exist_ok=True)
    logging.info("localizer start...")
    try:
        localizer = VisionLocalizer(camera_config_path, tube_config_path, picture_save_path)
        logging.info("VisionLocalizer successfully")
    except Exception as e:
	    logging.error(f"Faild to creat :{e}")
    logging.info("cap.start...")
    localizer.cap.start()
    delete_images_periodically(img_save_time, img_save_location)
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    logging.info("socket_bind...")
    server_socket.bind((ip, port))
    server_socket.listen(1)
    logging.info(f"Server listening on {ip}:{port}")
    try:
        while not shutdown_event.is_set():
            try:
                client_socket, address = server_socket.accept()
                client_thread = threading.Thread(target=client_handler, args=(client_socket, address))
                client_thread.start()
            except socket.timeout:
                continue
    except Exception as e:
        logging.error(f"Error in server_main: {e}")
    finally:
        logging.warning("Shutting down server...")
        try:
            server_socket.close()
        except Exception as e:
            logging.error(f"Error closing server socket: {e}")
        if localizer.cap.camera_is_connected() == 0:
            logging.warning("Releasing camera...")
            localizer.cap.release_camera()
        localizer.cap.join()
        logging.info("Server shutdown complete.")

# ------------------ UI 部分 ------------------

# 自定义 logging 处理器，将日志消息输出到 Tkinter Text 控件中
class TextHandler(std_logging.Handler):
    def __init__(self, text_widget):
        super().__init__()
        self.text_widget = text_widget

    def emit(self, record):
        msg = self.format(record)
        def append():
            self.text_widget.configure(state='normal')
            self.text_widget.insert(tk.END, msg + '\n')
            self.text_widget.configure(state='disabled')
            self.text_widget.yview(tk.END)
        self.text_widget.after(0, append)

# 启动服务器（在单独线程中运行 server_main）
def start_server():
    shutdown_event.clear()  # 清除关机信号
    server_thread = threading.Thread(target=server_main, daemon=True)
    server_thread.start()
    log_text.insert(tk.END, "Server started...\n")

# 发送关机信号并关闭服务器 socket
def shutdown_server():
    shutdown_event.set()
    global server_socket_global
    if server_socket_global:
        try:
            server_socket_global.close()
        except Exception as e:
            logging.error(f"Error closing server socket in shutdown: {e}")
    log_text.insert(tk.END, "Shutdown signal sent...\n")

# 构建 UI
root = tk.Tk()
root.title("控制面板")
root.geometry("900x700")
root.configure(bg="#1e1e1e")
root.attributes("-fullscreen", True)  # 启动时全屏

# 定义全屏切换函数
def toggle_fullscreen():
    is_fullscreen = root.attributes("-fullscreen")
    root.attributes("-fullscreen", not is_fullscreen)

# 顶部控制栏容器
header_frame = tk.Frame(root, bg="#1e1e1e")
header_frame.pack(fill=tk.X, padx=10, pady=10)

# 左侧Logo和标题区域
left_header = tk.Frame(header_frame, bg="#1e1e1e")
left_header.pack(side=tk.LEFT, anchor="w")

try:
    from PIL import Image, ImageTk
    original_logo = Image.open("hdt_pure_logo.png")
    desired_height = 32
    w, h = original_logo.size
    new_width = int(w * (desired_height / h))
    resized_logo = original_logo.resize((new_width, desired_height), Image.LANCZOS)
    logo_image = ImageTk.PhotoImage(resized_logo)
    logo_label = tk.Label(left_header, image=logo_image, bg="#1e1e1e")
    logo_label.image = logo_image
    logo_label.pack(side=tk.LEFT, padx=(0, 10))
except Exception as e:
    print("加载 logo 失败:", e)

title_label = tk.Label(left_header, text="控制面板", 
                      font=("Helvetica", 24, "bold"),  # 调小字号适应新布局
                      fg="white", bg="#1e1e1e")
title_label.pack(side=tk.LEFT, anchor="w")

# 右侧按钮容器
button_container = tk.Frame(header_frame, bg="#1e1e1e")
button_container.pack(side=tk.RIGHT, anchor="e")

# 字体大小控制（添加最大值限制）
log_font_size = 14
MAX_FONT_SIZE = 32  # 最大字体限制

def increase_font():
    global log_font_size
    if log_font_size < MAX_FONT_SIZE:  # 添加限制条件
        log_font_size += 2
        log_text.config(font=("Courier New", log_font_size))
        status_label.config(text=f"字体大小：{log_font_size}pt")

def decrease_font():
    global log_font_size
    if log_font_size > 8:
        log_font_size -= 2
        log_text.config(font=("Courier New", log_font_size))
        status_label.config(text=f"字体大小：{log_font_size}pt")
        
def shutdown_server():
    shutdown_event.set()
    global server_socket_global
    if server_socket_global:
        try:
            server_socket_global.close()
            log_text.insert(tk.END, "正在关闭服务器连接...\n")
        except Exception as e:
            logging.error(f"Error closing server socket in shutdown: {e}")
    else:
        log_text.insert(tk.END, "服务器未运行\n")
    
    # 添加1秒延迟确保资源释放
    root.after(1000, root.destroy)
        

# 控制按钮（调整顺序并添加分隔符）
control_buttons = [
    ("全屏切换", toggle_fullscreen, "#2196F3", "全屏切换"),
    ("放大日志", increase_font, "#2196F3", "放大日志"),
    ("缩小日志", decrease_font, "#2196F3", "缩小日志"),
    ("关闭服务", shutdown_server, "#F44336", "关机服务器")
]

for text, cmd, color, tooltip in control_buttons:
    btn = tk.Button(button_container, 
                   text=text,
                   command=cmd,
                   font=("Helvetica", 14, "bold"),
                   bg=color,
                   fg="white",
                   relief=tk.FLAT,
                   padx=12,
                   pady=6,
                   bd=0)
    btn.pack(side=tk.LEFT, padx=6)
    # 简单工具提示实现
    btn.bind("<Enter>", lambda e, t=tooltip: status_label.config(text=t))
    btn.bind("<Leave>", lambda e: status_label.config(text=""))

# 日志输出区域（调小高度给顶部留出空间）
log_text = ScrolledText(root, 
                       state='disabled', 
                       height=25,  # 减少高度
                       bg="black", 
                       fg="#00FF00", 
                       font=("Courier New", 14),
                       padx=10,
                       pady=10)
log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0,10))

# 状态提示栏
status_label = tk.Label(root, 
                       text="就绪",
                       bg="#1e1e1e",
                       fg="#666",
                       font=("Microsoft YaHei", 10),
                       anchor=tk.W)
status_label.pack(fill=tk.X, padx=10, pady=(0,5))

def auto_start():
    start_server()
    log_text.insert(tk.END, "服务器自动启动中...\n")

root.after(100, auto_start)

# 添加自定义日志处理器，使 logging 输出到日志框中
ui_text_handler = TextHandler(log_text)
formatter = std_logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
ui_text_handler.setFormatter(formatter)
logging.addHandler(ui_text_handler)


root.mainloop()
