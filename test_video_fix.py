#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试视频保存修复 - 解决1KB文件问题
"""

import cv2
import os
import numpy as np

def create_test_video():
    """创建一个测试视频用于调试"""
    print("🎬 创建测试视频...")
    
    # 视频参数
    width, height = 1320, 1080  # 裁剪后的尺寸 (1920-600=1320)
    fps = 30
    duration = 3  # 3秒
    total_frames = fps * duration
    
    # 创建视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'XVID')
    out = cv2.VideoWriter('test_input.avi', fourcc, fps, (width, height), isColor=True)
    
    if not out.isOpened():
        print("❌ 无法创建测试视频")
        return False
    
    # 生成帧
    for i in range(total_frames):
        # 创建彩色帧
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 添加渐变背景
        for y in range(height):
            frame[y, :] = [int(255 * y / height), 100, int(255 * (1 - y / height))]
        
        # 添加移动的矩形
        x = int((i / total_frames) * (width - 200))
        y = height // 2 - 50
        cv2.rectangle(frame, (x, y), (x + 200, y + 100), (0, 255, 0), -1)
        
        # 添加文字
        text = f"Frame {i+1}/{total_frames}"
        cv2.putText(frame, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        out.write(frame)
    
    out.release()
    print("✅ 测试视频创建完成: test_input.avi")
    return True

def test_video_processing():
    """测试视频处理流程"""
    print("🔧 测试视频处理流程...")
    
    # 输入和输出路径
    input_video = "test_input.avi"
    output_video = "test_output.avi"
    
    if not os.path.exists(input_video):
        print("❌ 输入视频不存在，先创建测试视频")
        if not create_test_video():
            return False
    
    # 打开输入视频
    cap = cv2.VideoCapture(input_video)
    if not cap.isOpened():
        print("❌ 无法打开输入视频")
        return False
    
    # 获取视频属性
    frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"📹 输入视频信息:")
    print(f"   分辨率: {frame_width} x {frame_height}")
    print(f"   帧率: {fps} FPS")
    print(f"   总帧数: {total_frames}")
    
    # 创建输出视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'XVID')
    out = cv2.VideoWriter(output_video, fourcc, fps, (frame_width, frame_height), isColor=True)
    
    if not out.isOpened():
        print("❌ 无法创建输出视频写入器")
        cap.release()
        return False
    
    print("✅ 输出视频写入器创建成功")
    
    # 处理视频帧
    frame_count = 0
    success_count = 0
    
    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        print(f"处理第 {frame_count}/{total_frames} 帧", end=' ')
        
        # 简单处理：添加一个红色矩形
        processed_frame = frame.copy()
        cv2.rectangle(processed_frame, (50, 50), (200, 150), (0, 0, 255), 3)
        cv2.putText(processed_frame, f"Processed {frame_count}", (50, 200), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
        
        # 写入帧
        success = out.write(processed_frame)
        if success:
            success_count += 1
            print("✅")
        else:
            print("❌")
        
        # 限制处理帧数
        if frame_count >= 30:  # 只处理30帧
            break
    
    # 释放资源
    cap.release()
    out.release()
    
    print(f"\n📊 处理结果:")
    print(f"   处理帧数: {frame_count}")
    print(f"   成功写入: {success_count}")
    
    # 验证输出文件
    if os.path.exists(output_video):
        file_size = os.path.getsize(output_video)
        print(f"   输出文件大小: {file_size / (1024*1024):.2f} MB")
        
        if file_size > 1024:  # 大于1KB
            print("✅ 输出视频大小正常")
            
            # 验证可读性
            test_cap = cv2.VideoCapture(output_video)
            if test_cap.isOpened():
                ret, test_frame = test_cap.read()
                if ret:
                    print("✅ 输出视频可以正常读取")
                    print(f"   输出视频分辨率: {test_frame.shape[1]} x {test_frame.shape[0]}")
                else:
                    print("❌ 输出视频无法读取帧")
                test_cap.release()
            else:
                print("❌ 输出视频无法打开")
        else:
            print("❌ 输出视频文件过小，可能写入失败")
    else:
        print("❌ 输出视频文件不存在")
    
    return True

def check_opencv_codecs():
    """检查OpenCV支持的编码器"""
    print("🔍 检查OpenCV支持的编码器...")
    
    # 测试不同的编码器
    codecs = ['XVID', 'MJPG', 'mp4v', 'H264', 'X264']
    test_size = (640, 480)
    
    for codec in codecs:
        try:
            fourcc = cv2.VideoWriter_fourcc(*codec)
            test_file = f"test_{codec.lower()}.avi"
            out = cv2.VideoWriter(test_file, fourcc, 30, test_size, isColor=True)
            
            if out.isOpened():
                print(f"✅ {codec} 编码器可用")
                out.release()
                # 清理测试文件
                if os.path.exists(test_file):
                    os.remove(test_file)
            else:
                print(f"❌ {codec} 编码器不可用")
        except Exception as e:
            print(f"❌ {codec} 编码器测试失败: {e}")

def main():
    print("🎥 视频保存问题诊断工具")
    print("=" * 50)
    
    # 检查编码器
    check_opencv_codecs()
    print()
    
    # 测试视频处理
    success = test_video_processing()
    
    if success:
        print("\n🎉 测试完成！如果输出视频正常，说明修复成功")
        print("💡 现在可以运行修复后的 text/untitled-2.py")
    else:
        print("\n❌ 测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
