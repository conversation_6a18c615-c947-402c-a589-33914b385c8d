import socket
import pickle
import json
import struct
import threading
import time
import os
from functools import partial

import cv2

from client import send_command
from server_utils.vision_localizer import VisionLocalizer
from server_utils.logger_config import logger as logging
from func.chessboard import CameraCapture

# ====== 全局变量保持不变 ======
camera = cv2.VideoCapture(0)
camera.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

shutdown_event = threading.Event()
server_socket_global = None
log_text = None
root = None


# ====== 封装模块开始 ======

class ServerConfigLoader:
    @staticmethod
    def load(config_file, part_id):
        if not os.path.exists(config_file):
            raise FileNotFoundError(f"Config file does not exist: {config_file}")
        with open(config_file, "r") as f:
            config = json.load(f)
        try:
            ip = config["server_config"]["ip"]
            port = config["server_config"]["port"]
            camera_config_path = os.path.join(os.path.dirname(__file__), config["localizer_config"]["camera_config_path"])
            tube_config_path_dict = config["localizer_config"]["tube_configs"]
            tube_config_info = tube_config_path_dict.get(str(part_id))
            if not tube_config_info:
                raise KeyError(f"Invalid part_id: {part_id} (No config found)")
            tube_config_path = os.path.join(os.path.dirname(__file__), tube_config_info["tube_config_path"])
            img_save_time = config["save_days"]["days_to_keep"]
            img_save_location = os.path.join(os.path.dirname(__file__), config["save_days"]["image_folder_path"])
            os.makedirs(img_save_location, exist_ok=True)

            logging.info(f"Server config details: IP:{ip}, Port:{port}, "
                         f"Camera Config:{camera_config_path}, Tube Config:{tube_config_path}, "
                         f"Save Days:{img_save_time}, Save Path:{img_save_location}")

            return ip, port, camera_config_path, tube_config_path, img_save_time, img_save_location
        except KeyError as e:
            raise KeyError(f"Missing key in config file: {e}")


class ImageSender:
    @staticmethod
    def _send_with_len(sock, img):
        _, encoded = cv2.imencode(".png", img)
        data = encoded.tobytes()
        sock.sendall(struct.pack("!I", len(data)))  # 先发长度
        sock.sendall(data)  # 再发图像

    @staticmethod
    def send(imgL, imgR, client_socket):
        ImageSender._send_with_len(client_socket, imgL)
        ImageSender._send_with_len(client_socket, imgR)


class ClientCommandHandler:
    def __init__(self, localizer):
        self.localizer = localizer

    def handle(self, client_socket, address):
        try:
            logging.warning("))))))))____")
            while True:
                command = client_socket.recv(1024).decode("utf-8").strip()
                if not command:
                    break

                # 原样保留命令判断逻辑
                if command == "Q_0":
                    logging.warning(f"command: {command}")
                    camera_capture = CameraCapture()
                    camera_capture.start()
                    # 启动或重启捕获
                    frames = camera_capture.get_latest_frames()
                    if frames[0] is not None and frames[1] is not None:
                        self._send_with_len(client_socket, frames)
                        print("Chessboard detected, ready to save.")
                    else:
                        print("No chessboard detected.")
                    continue

                elif command == "Q_0_S":
                    try:
                        logging.warning(f"command: {command}")
                        left_path, right_path = camera_capture.save_current_frame()
                        result = f"{left_path}|{right_path}"
                        client_socket.sendall(struct.pack("!I", len(result)) + result.encode("utf-8"))
                    except Exception as e:
                        err_msg = f"Error: {e}"
                        client_socket.sendall(struct.pack("!I", len(err_msg)) + err_msg.encode("utf-8"))
                    continue

                elif command == "Q_0_N":
                    logging.warning(f"command: {command}")
                    # # 切换到下一组图像（模拟）
                    # success = localizer.next_chessboard_image()
                    # client_socket.sendall(struct.pack("!i", 1 if success else 0))
                    continue

                elif command == "C_0":
                    logging.warning(f"command: {command}")
                    # 返回相机标定路径
                    # path = localizer.get_camera_calibrate_path()
                    # client_socket.sendall(path.encode("utf-8"))
                    continue

                elif command == "T_0":
                    logging.warning(f"command: {command}")
                    # 获取导管图像对
                    # left_img, right_img = localizer.get_tube_images()
                    # _send_with_len(client_socket, left_img)
                    # _send_with_len(client_socket, right_img)
                    continue

                elif command == "T_0_S":
                    logging.warning(f"command: {command}")
                    # 导管标定确认
                    # success = localizer.confirm_tube_calibration()
                    # client_socket.sendall(struct.pack("!i", 1 if success else 0))
                    continue

                elif command == "S_M_Y":
                    logging.warning(f"command: {command}")
                    # 返回视觉模型路径列表
                    # paths = localizer.get_model_paths()
                    # client_socket.sendall(";".join(paths).encode("utf-8"))
                    continue

                elif command == "C_M":
                    logging.warning(f"command: {command}")
                    # 返回相机配置文件路径
                    # path = localizer.get_camera_config_path()
                    # client_socket.sendall(path.encode("utf-8"))
                    continue

                else:
                    logging.warning(f"Unknown command: {command}")

        except Exception as e:
            logging.error(f"Error in client_handler: {e}")
        finally:
            client_socket.close()
            logging.info(f"Client disconnected: {address}")

    def handle_custom_command(self, command, client_socket):
        # 保留原始解析逻辑
        command_body = command[1:]
        if "@" in command_body:
            command_main, extra_str = command_body.split("@", 1)
        else:
            command_main, extra_str = command_body, None

        command_parts = command_main.split("&")
        if len(command_parts) != 2:
            client_socket.sendall(struct.pack("!i", -1))
            return

        command_id, tube_str = command_parts
        try:
            if tube_str.isdigit():
                tube_diameter = int(tube_str)
            elif "." in tube_str and tube_str.replace(".", "").isdigit():
                tube_diameter = float(tube_str)
            else:
                client_socket.sendall(struct.pack("!i", -1))
                return
        except ValueError:
            client_socket.sendall(struct.pack("!i", -1))
            return

        # 这里可以扩展额外参数保存逻辑
        client_socket.sendall(struct.pack("!i", 1))


class LogTextHandler(logging.Handler):
    def __init__(self, text_widget):
        super().__init__()
        self.text_widget = text_widget

    def emit(self, record):
        msg = self.format(record)
        def append():
            self.text_widget.configure(state='normal')
            self.text_widget.insert(tk.END, msg + '\n')
            self.text_widget.configure(state='disabled')
            self.text_widget.yview(tk.END)
        self.text_widget.after(0, append)


def delete_old_images(img_save_time, img_save_location):
    current_time = time.time()
    for filename in os.listdir(img_save_location):
        file_path = os.path.join(img_save_location, filename)
        if os.path.isfile(file_path) and filename.lower().endswith((".jpg", ".jpeg", ".png", ".bmp", ".gif")):
            file_mtime = os.path.getmtime(file_path)
            file_age_days = (current_time - file_mtime) / (60 * 60 * 24)
            if file_age_days > img_save_time:
                logging.info(f"Deleting file: {file_path} (Age: {int(file_age_days)} days)")
                os.remove(file_path)

def delete_images_periodically(img_save_time, img_save_location, interval=86400):
    delete_old_images(img_save_time, img_save_location)
    threading.Timer(interval, delete_images_periodically, args=(img_save_time, img_save_location, interval)).start()


# ====== 主服务启动函数保持不变 ======
def server_main():
    logging.info("Starting server...")
    config_file_path = os.path.join(os.path.dirname(__file__), "configs/config.json")
    part_id = "6"
    try:
        ip, port, camera_config_path, tube_config_path, img_save_time, img_save_location = ServerConfigLoader.load(
            config_file_path, part_id)
    except ValueError as e:
        logging.warning(f"Configuration warning: {e}")
        return

    logging.info("Initializing localizer...")
    picture_save_path = os.path.join(os.path.dirname(__file__), "saved_pictures")
    logging.info(f"open {picture_save_path}")
    localizer = VisionLocalizer(camera_config_path, tube_config_path, picture_save_location)
    logging.info(f"Server localizer on {localizer}")
    localizer.cap.start()

    delete_images_periodically(img_save_time, img_save_location)

    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    server_socket.bind((ip, port))
    server_socket.listen(1)
    logging.info(f"Server listening on {ip}:{port}")

    try:
        while True:
            client_socket, address = server_socket.accept()
            handler = ClientCommandHandler(localizer)
            client_thread = threading.Thread(target=handler.handle, args=(client_socket, address))
            client_thread.start()
    except KeyboardInterrupt:
        logging.warning("KeyboardInterrupt, shutting down server...")
    finally:
        if localizer.cap.camera_is_connected() == 0:
            localizer.cap.release_camera()
        localizer.cap.join()
        server_socket.close()


# ====== GUI 启动逻辑保持不变 ======
def start_server():
    global log_text
    server_thread = threading.Thread(target=server_main_wrapper, daemon=True)
    server_thread.start()
    log_text.insert(tk.END, "服务器启动中...\n")

def server_main_wrapper():
    try:
        server_main()
    except Exception as e:
        logging.error(f"服务器异常: {e}")
    finally:
        global server_socket_global
        server_socket_global = None

def shutdown_server():
    shutdown_event.set()
    global server_socket_global
    if server_socket_global:
        try:
            server_socket_global.close()
            log_text.insert(tk.END, "正在关闭服务器连接...\n")
        except Exception as e:
            logging.error(f"关闭服务器异常: {e}")
    else:
        log_text.insert(tk.END, "服务器未运行\n")
    root.after(1000, root.destroy)


# ====== 主程序入口保持不变 ======
if __name__ == "__main__":
    import tkinter as tk
    from tkinter.scrolledtext import ScrolledText

    root = tk.Tk()
    root.title("视觉定位服务器控制台")
    root.geometry("1200x800")
    root.configure(bg="#2e2e2e")

    log_text = ScrolledText(root,
                          state='disabled',
                          bg="black",
                          fg="#00FF00",
                          font=("Consolas", 12),
                          padx=10,
                          pady=10)
    log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    control_frame = tk.Frame(root, bg="#2e2e2e")
    control_frame.pack(fill=tk.X, padx=10, pady=5)

    btn_style = {
        "font": ("Arial", 12),
        "width": 15,
        "relief": tk.GROOVE,
        "bd": 2
    }

    shutdown_btn = tk.Button(control_frame,
                            text="关闭服务",
                            command=shutdown_server,
                            bg="#F44336",
                            fg="white",
                            **btn_style)
    shutdown_btn.pack(side=tk.RIGHT, padx=5)

    ui_handler = LogTextHandler(log_text)
    formatter = std_logging.Formatter("%(asctime)s [%(levelname)s] %(message)s")
    ui_handler.setFormatter(formatter)
    std_logging.getLogger().addHandler(ui_handler)

    root.after(100, start_server)
    root.mainloop()
