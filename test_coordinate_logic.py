#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试坐标处理逻辑
验证视觉判定函数的坐标计算是否正确
"""

import struct
import socket
import time

def pack_plc_command(pos, heartbeat, manual_flag, auto_flag, **kwargs):
    """打包PLC命令数据"""
    pos_bytes = struct.pack("!6f", *pos)
    
    control_list = [
        heartbeat,                                    # 25 心跳
        manual_flag,                                  # 26 手动
        auto_flag,                                    # 27 自动
        kwargs.get("preparing_request", 0),           # 28 准备请求
        255 if kwargs.get("plc_ready_clamp", False) else 0,  # 29 PLC准备入夹
        255 if kwargs.get("not_in_position", False) else 0,  # 30 未到位
        255 if kwargs.get("in_position", False) else 0,      # 31 到位
        255 if kwargs.get("start_judgment", False) else 0,   # 32 开始判定
        0,  # 33
        0,  # 34
        0,  # 35
        255 if kwargs.get("result_received", False) else 0,  # 36 结果确认
        0,  # 37
        0,  # 38
        0,  # 39
        0,  # 40
        0,  # 41
        0   # 42
    ]
    
    # 填充为48字节
    full_bytes = pos_bytes + bytes(control_list) + bytes(48 - 24 - len(control_list))
    return full_bytes

def parse_response(data):
    """解析响应数据"""
    if len(data) != 48:
        return None
    
    pos_floats = struct.unpack("!6f", data[:24])
    controls = struct.unpack("!18B", data[24:42])
    
    return {
        "position": pos_floats,
        "heartbeat": controls[0],
        "manual": controls[1] == 255,
        "auto": controls[2] == 255,
        "preparing": controls[3],
        "ready": controls[4],
        "clamp_ack": controls[5],
        "judging": controls[6],
        "judgment_complete": controls[7],
        "result_ng": controls[15] == 255,
        "result_ok": controls[16] == 255
    }

def test_coordinate_flow():
    """测试完整的坐标处理流程"""
    print("🧪 测试坐标处理逻辑")
    print("=" * 50)
    
    # 测试坐标
    test_coordinates = [100.5, 200.3, 300.7, 10.2, 20.1, 30.8]
    print(f"📍 测试PLC坐标: {test_coordinates}")
    
    try:
        # 连接到PLC服务器
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10.0)
        sock.connect(('127.0.0.1', 2000))
        print("✅ 连接成功")
        
        heartbeat = 1
        
        # 步骤1: 启动自动模式
        print("\n--- 步骤1: 启动自动模式 ---")
        cmd = pack_plc_command(test_coordinates, heartbeat, 0, 255)
        sock.sendall(cmd)
        response = sock.recv(48)
        parsed = parse_response(response)
        print(f"响应坐标: {parsed['position']}")
        print(f"自动模式: {parsed['auto']}, 准备状态: {parsed['preparing']}")
        
        time.sleep(1)
        heartbeat += 1
        
        # 步骤2: 等待准备完成
        print("\n--- 步骤2: 等待准备完成 ---")
        for i in range(3):
            cmd = pack_plc_command(test_coordinates, heartbeat, 0, 255)
            sock.sendall(cmd)
            response = sock.recv(48)
            parsed = parse_response(response)
            print(f"第{i+1}次 - 响应坐标: {parsed['position']}")
            print(f"准备: {parsed['preparing']}, 就绪: {parsed['ready']}")
            
            if parsed['ready'] == 10:
                print("✅ 准备就绪!")
                break
            
            time.sleep(0.5)
            heartbeat += 1
        
        # 步骤3: PLC准备入夹
        print("\n--- 步骤3: PLC准备入夹 ---")
        cmd = pack_plc_command(test_coordinates, heartbeat, 0, 255, plc_ready_clamp=True)
        sock.sendall(cmd)
        response = sock.recv(48)
        parsed = parse_response(response)
        print(f"响应坐标: {parsed['position']}")
        print(f"入夹确认: {parsed['clamp_ack']}")
        
        time.sleep(1)
        heartbeat += 1
        
        # 步骤4: 到位
        print("\n--- 步骤4: 设置到位 ---")
        cmd = pack_plc_command(test_coordinates, heartbeat, 0, 255, in_position=True)
        sock.sendall(cmd)
        response = sock.recv(48)
        parsed = parse_response(response)
        print(f"响应坐标: {parsed['position']}")
        
        time.sleep(1)
        heartbeat += 1
        
        # 步骤5: 开始判定 - 关键测试点
        print("\n--- 步骤5: 开始判定（关键测试）---")
        print(f"🔍 发送PLC坐标: {test_coordinates}")
        cmd = pack_plc_command(test_coordinates, heartbeat, 0, 255, start_judgment=True)
        sock.sendall(cmd)
        response = sock.recv(48)
        parsed = parse_response(response)
        
        print(f"📤 判定中响应坐标: {parsed['position']}")
        print(f"判定状态: {parsed['judging']}")
        
        # 等待判定完成
        time.sleep(2)
        heartbeat += 1
        
        # 步骤6: 获取判定结果和最终坐标
        print("\n--- 步骤6: 获取判定结果 ---")
        cmd = pack_plc_command(test_coordinates, heartbeat, 0, 255)
        sock.sendall(cmd)
        response = sock.recv(48)
        parsed = parse_response(response)
        
        print(f"📥 最终响应坐标: {parsed['position']}")
        print(f"判定完成: {parsed['judgment_complete']}")
        print(f"结果OK: {parsed['result_ok']}")
        print(f"结果NG: {parsed['result_ng']}")
        
        # 分析坐标变化
        print(f"\n📊 坐标分析:")
        print(f"原始PLC坐标: {test_coordinates}")
        print(f"最终返回坐标: {list(parsed['position'])}")
        
        coordinate_diff = [parsed['position'][i] - test_coordinates[i] for i in range(6)]
        print(f"坐标差值: {coordinate_diff}")
        
        if parsed['result_ok']:
            print("✅ 判定OK - 应该返回PLC原始坐标")
            if all(abs(diff) < 0.001 for diff in coordinate_diff):
                print("✅ 坐标验证通过：返回了PLC原始坐标")
            else:
                print("❌ 坐标验证失败：坐标发生了变化")
        elif parsed['result_ng']:
            print("⚠️ 判定NG - 应该返回修正后坐标")
            if any(abs(diff) > 0.001 for diff in coordinate_diff):
                print("✅ 坐标验证通过：返回了修正后坐标")
            else:
                print("❌ 坐标验证失败：坐标没有修正")
        
        # 步骤7: 确认结果
        print("\n--- 步骤7: 确认结果 ---")
        cmd = pack_plc_command(test_coordinates, heartbeat, 0, 255, result_received=True)
        sock.sendall(cmd)
        response = sock.recv(48)
        parsed = parse_response(response)
        print(f"确认后响应坐标: {parsed['position']}")
        
        print("\n🎉 坐标处理流程测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    finally:
        try:
            sock.close()
        except:
            pass

def test_multiple_coordinates():
    """测试多组不同的坐标"""
    print("\n🔄 测试多组坐标")
    print("=" * 30)
    
    test_cases = [
        [0.0, 0.0, 0.0, 0.0, 0.0, 0.0],           # 零坐标
        [100.0, 200.0, 300.0, 0.0, 0.0, 0.0],     # 基本坐标
        [150.5, 250.3, 350.7, 15.2, 25.1, 35.8],  # 小数坐标
        [-50.0, -100.0, 200.0, -10.0, 5.0, -15.0] # 负数坐标
    ]
    
    for i, coords in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {coords}")
        # 这里可以调用简化的测试逻辑
        # 为了简化，只打印测试用例
        print(f"  预期：根据视觉判定结果返回相应坐标")

if __name__ == "__main__":
    print("🧪 PLC坐标处理逻辑测试工具")
    print("=" * 60)
    
    # 主要测试
    test_coordinate_flow()
    
    # 多组坐标测试
    test_multiple_coordinates()
    
    print("\n💡 测试说明:")
    print("1. OK结果：返回PLC原始坐标")
    print("2. NG结果：返回PLC坐标+偏移量")
    print("3. 检查坐标差值来验证逻辑正确性")
