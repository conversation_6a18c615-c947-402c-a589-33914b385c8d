# PLC测试UI使用说明

## 📋 概述

我为您创建了两个版本的PLC测试UI：

1. **`simple_plc_test_ui.py`** - 简化版（推荐使用）
2. **`plc_test_ui.py`** - 完整版（功能更全但可能有布局问题）

## 🚀 启动方式

```bash
# 启动简化版（推荐）
python simple_plc_test_ui.py

# 或启动完整版
python plc_test_ui.py
```

## 🖥️ 界面布局

### 左侧：发送控制面板

#### 1. 连接控制
- **IP地址**：默认127.0.0.1，可修改为*************
- **端口**：默认2000
- **连接按钮**：点击连接/断开PLC服务器

#### 2. 坐标数据
- **X, Y, Z**：位置坐标
- **RX, RY, RZ**：旋转角度
- 支持浮点数输入

#### 3. 控制位设置（25-42位）
重要控制位说明：
- **25位 心跳**：SpinBox，0-255，用于连接检测
- **26位 手动模式**：CheckBox，勾选=255（启动手动模式）
- **27位 自动模式**：CheckBox，勾选=255（启动自动模式）
- **29位 PLC准备入夹**：CheckBox，勾选=255（PLC准备开始入夹）
- **30位 未到位**：CheckBox，勾选=255（PLC未到位）
- **31位 到位**：CheckBox，勾选=255（PLC已到位）
- **32位 开始判定**：CheckBox，勾选=255（开始视觉判定）
- **36位 结果确认**：CheckBox，勾选=255（确认收到判定结果）

#### 4. 快捷操作按钮
- **清空**：清除所有控制位
- **手动模式**：快速设置手动模式（26位=ON，27位=OFF）
- **自动模式**：快速设置自动模式（26位=OFF，27位=ON）
- **PLC准备**：设置29位=ON
- **到位**：设置30位=OFF，31位=ON
- **开始判定**：设置32位=ON

#### 5. 发送按钮
- **发送数据**：手动发送一次48字节数据到PLC

### 右侧：响应显示面板

#### 1. 连接状态
- 显示当前连接状态（未连接/已连接/错误信息）
- 颜色编码：红色=断开，绿色=连接，橙色=错误

#### 2. 响应坐标
- 显示PLC返回的6个坐标值
- 实时更新，保留3位小数

#### 3. 响应控制位
重要响应位说明：
- **25位 心跳**：显示心跳计数值
- **26位 手动确认**：显示手动模式确认状态
- **27位 自动确认**：显示自动模式确认状态
- **28位 准备状态**：特殊值9=准备中
- **29位 就绪状态**：特殊值10=就绪
- **30位 入夹确认**：特殊值20=确认入夹
- **31位 判定状态**：特殊值30=判定中
- **32位 判定完成**：特殊值40=判定完成
- **40位 结果NG**：255=NG结果
- **41位 结果OK**：255=OK结果

#### 4. 原始数据显示
- **发送数据**：显示发送的48字节十六进制数据
- **接收数据**：显示接收的48字节十六进制数据
- 格式化显示，每字节用空格分隔

## 🔄 使用流程

### 测试自动模式完整流程

1. **启动服务器**
   ```bash
   python test_plc_server.py  # 或启动完整的orangepi_server.py
   ```

2. **启动测试UI**
   ```bash
   python simple_plc_test_ui.py
   ```

3. **连接到服务器**
   - 确认IP和端口正确
   - 点击"连接"按钮
   - 状态栏显示绿色"已连接"

4. **测试自动模式流程**

   **步骤1：启动自动模式**
   - 点击"自动模式"快捷按钮
   - 点击"发送数据"
   - 观察响应：27位应显示"ON"，28位应显示"9"

   **步骤2：等待准备完成**
   - 再次点击"发送数据"
   - 观察响应：29位应显示"10"（就绪）

   **步骤3：PLC准备入夹**
   - 点击"PLC准备"按钮
   - 点击"发送数据"
   - 观察响应：30位应显示"20"（确认入夹）

   **步骤4：设置到位**
   - 点击"到位"按钮
   - 点击"发送数据"
   - 观察响应状态变化

   **步骤5：开始判定**
   - 点击"开始判定"按钮
   - 点击"发送数据"
   - 观察响应：31位应显示"30"（判定中），然后32位显示"40"（完成）
   - 40位或41位显示判定结果

## 🎯 关键特性

### 1. 实时数据显示
- 每次发送后立即显示响应数据
- 控制位状态用颜色编码
- 原始数据十六进制显示

### 2. 快捷操作
- 一键设置常用模式
- 快速清空所有设置
- 心跳自动递增

### 3. 错误处理
- 连接超时检测
- 异常信息显示
- 自动断开保护

### 4. 数据格式
- **发送格式**：前24字节坐标 + 后24字节控制位
- **接收格式**：前24字节坐标 + 后24字节状态位
- **十六进制显示**：便于调试和分析

## 🔧 调试技巧

### 1. 查看日志
```bash
# 实时查看服务器日志
tail -f orangepi_server_run_log.log
```

### 2. 检查连接
- 确保服务器在2000端口监听
- 使用netstat检查端口状态
- 尝试不同的IP地址

### 3. 数据分析
- 对比发送和接收的十六进制数据
- 检查特定位的状态变化
- 验证状态机转换逻辑

## ⚠️ 注意事项

1. **服务器必须先启动**：确保PLC服务器在运行
2. **IP地址配置**：根据实际网络环境调整IP
3. **数据格式**：严格按照48字节格式
4. **状态同步**：发送后检查响应状态
5. **连接管理**：使用完毕后断开连接

## 🚀 扩展功能

如需添加更多功能，可以在以下位置修改：

1. **添加新控制位**：在`important_bits`字典中添加
2. **添加新响应位**：在`response_bits`字典中添加
3. **添加快捷操作**：在`shortcuts`列表中添加
4. **修改数据格式**：在`send_data()`和`parse_response()`函数中修改

这个测试UI为您提供了完整的PLC通信测试能力，可以方便地验证所有的通信协议和状态机逻辑！
