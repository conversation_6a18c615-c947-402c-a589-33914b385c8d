# PLC坐标处理逻辑说明

## 📋 需求理解

您希望实现以下逻辑：
1. **OK判定**：误差在区间内 → 直接返回PLC发送的原始坐标
2. **NG判定**：误差在区间外 → 返回偏移坐标+PLC坐标的修正结果

## ✅ 已实现的修改

### 1. **修改 `perform_vision_judgment` 函数**

```python
def perform_vision_judgment(localizer, plc_coordinates):
    """
    执行视觉判定，返回判定结果和坐标
    
    Args:
        localizer: 视觉定位器对象
        plc_coordinates: PLC发送的坐标 [x, y, z, rx, ry, rz]
    
    Returns:
        tuple: (判定结果, 返回坐标)
        - 判定结果: "OK" 或 "NG"
        - 返回坐标: 
            * OK时：直接返回PLC坐标
            * NG时：返回偏移坐标+PLC坐标
    """
```

**核心逻辑：**
- 调用 `localizer.main_rectify_poses(save=True)` 获取偏移量
- 检查偏移量是否在容差范围内
- **OK情况**：`return_coords = plc_coordinates.copy()`
- **NG情况**：`return_coords = [plc_coordinates[i] + offset_coords[i] for i in range(6)]`

### 2. **修改状态机调用逻辑**

```python
elif auto_state == "judging":
    # 获取PLC发送的坐标
    plc_coordinates = list(parsed_data["position"])
    
    # 调用视觉判定函数，传入PLC坐标
    judgment_result, return_coordinates = perform_vision_judgment(localizer, plc_coordinates)
    
    # 保存结果
    last_judgment_result = judgment_result
    last_return_coordinates = return_coordinates
```

### 3. **修改坐标返回逻辑**

```python
# 根据状态机结果决定返回的坐标
if auto_state == "result_sent" and last_return_coordinates:
    # 返回视觉判定后的坐标
    current_pos = last_return_coordinates.copy()
else:
    # 返回PLC发送的原始坐标
    current_pos = list(parsed_data["position"])
```

## 🔄 完整工作流程

### 步骤1：PLC发送坐标和判定请求
```
PLC → 香橙派: [100.0, 200.0, 300.0, 10.0, 20.0, 30.0] + 开始判定标志
```

### 步骤2：视觉系统检测偏移
```python
# 视觉系统返回偏移量
vision_result = localizer.main_rectify_poses(save=True)
# 例如：[1, 2.5, -1.8, 0.5, 0.2, -0.1, 0.3]
#       [状态, x偏移, y偏移, z偏移, rx偏移, ry偏移, rz偏移]
```

### 步骤3：判定逻辑
```python
# 检查偏移是否在容差内
position_tolerance = 5.0  # mm
angle_tolerance = 5.0     # 度

if 偏移量 < 容差:
    result = "OK"
    return_coords = PLC原始坐标  # [100.0, 200.0, 300.0, 10.0, 20.0, 30.0]
else:
    result = "NG" 
    return_coords = PLC坐标 + 偏移量  # [102.5, 198.2, 300.5, 10.2, 19.9, 30.3]
```

### 步骤4：返回结果给PLC
```
香橙派 → PLC: 
- 判定结果: OK/NG标志位
- 坐标数据: 根据判定结果返回相应坐标
```

## 📊 数据流示例

### 示例1：OK情况
```
输入：PLC坐标 [100.0, 200.0, 300.0, 10.0, 20.0, 30.0]
检测：偏移量 [1.0, -0.5, 0.2, 0.1, -0.1, 0.05]
判定：偏移 < 5.0mm → OK
输出：返回坐标 [100.0, 200.0, 300.0, 10.0, 20.0, 30.0] (PLC原始坐标)
```

### 示例2：NG情况
```
输入：PLC坐标 [100.0, 200.0, 300.0, 10.0, 20.0, 30.0]
检测：偏移量 [8.5, -6.2, 2.1, 0.8, -0.3, 1.2]
判定：偏移 > 5.0mm → NG
输出：返回坐标 [108.5, 193.8, 302.1, 10.8, 19.7, 31.2] (修正后坐标)
```

## 🔧 关键配置参数

### 容差设置
```python
position_tolerance = 5.0  # 位置容差 (mm)
angle_tolerance = 5.0     # 角度容差 (度)
```

### 全局变量
```python
last_judgment_result = None      # 最后判定结果 "OK"/"NG"
last_return_coordinates = [...]  # 最后返回的坐标
```

## 🧪 测试验证

### 运行测试脚本
```bash
# 启动PLC服务器
python test_plc_server.py

# 运行坐标逻辑测试
python test_coordinate_logic.py
```

### 验证要点
1. **OK情况**：返回坐标 = PLC输入坐标
2. **NG情况**：返回坐标 = PLC输入坐标 + 偏移量
3. **坐标精度**：保持浮点数精度
4. **状态同步**：判定结果与坐标返回一致

## ⚠️ 注意事项

### 1. 坐标单位一致性
- 确保PLC坐标和视觉系统偏移量使用相同单位
- 位置通常使用mm，角度使用度

### 2. 容差参数调整
- 根据实际精度要求调整 `position_tolerance` 和 `angle_tolerance`
- 过小可能导致过多NG，过大可能影响精度

### 3. 异常处理
- 视觉系统失败时返回PLC原始坐标
- 网络异常时保持连接稳定性

### 4. 日志记录
- 详细记录PLC输入坐标、检测偏移量、判定结果、返回坐标
- 便于调试和质量追踪

## 🎯 预期效果

修改后的系统将：
1. ✅ 根据视觉检测结果智能返回坐标
2. ✅ OK时保持PLC原始坐标不变
3. ✅ NG时返回精确修正的坐标
4. ✅ 提供完整的判定和坐标追踪日志
5. ✅ 支持实时测试和验证

这样PLC就能根据返回的坐标进行精确的位置控制了！
