import ast
import errno
import select
import shutil
import socket
import pickle
import json
import struct
import threading
import logging as std_logging
from threading import Event
import time
import os
import io
from functools import partial
from func.tube import TubeCalibrator
import cv2
from func.camera import calibrate_camera
from client import send_command
from server_utils.vision_localizer import VisionLocalizer
# from func.chess import ChessboardCaptureThread
from server_utils.logger_config import logger as logging
from func.chessboard import ChessboardCaptureThread

camera_params_path = r'/home/<USER>/Desktop/orangepi_server/configs/camera_params.pkl'
model_path = r'/home/<USER>/Desktop/orangepi_server/models/model.rknn'
config_path_1 = r'config.json'
out_folder = r'new_config'
tube_config_path_1 = r'tube_config.pkl'
real_path = r"/home/<USER>/Desktop/orangepi_server/configs/path"
camera_capture = None
aging_active = True
aging_lock = threading.Lock()
t0_stop_event = threading.Event()
t0_thread = None
q0_stop_event = threading.Event()
q0_thread = None
s_stop_event = threading.Event()
s_thread = None


def read_server_config(config_file, part_id):
    """
    读取配置文件中的：
        IP/Port
        相机配置路径
        与特定管件 part_id 对应的 .pkl 配置路径
        图片保存时长和路径
    """
    logging.info(f"Reading server config from: {config_file}")
    if not os.path.exists(config_file):
        raise FileNotFoundError(f"Config file does not exist: {config_file}")
    with open(config_file, "r") as f:
        config = json.load(f)
    try:
        ip = config["server_config"]["ip"]
        port = config["server_config"]["port"]
        if not isinstance(ip, str) or not ip:
            logging.error("Invalid IP address in config.")
        if not isinstance(port, int) or port <= 0:
            logging.error("Invalid port in config.")
        camera_config_path = os.path.join(os.path.dirname(__file__), config["localizer_config"]["camera_config_path"])
        if not os.path.exists(camera_config_path):
            logging.error(f"Camera config path does not exist: {camera_config_path}")
        tube_config_path_dict = config["localizer_config"]["tube_configs"]
        tube_config_info = tube_config_path_dict.get(str(part_id))
        if not tube_config_info:
            logging.error(f"Invalid part_id: {part_id} (No config found)")
        tube_config_path = os.path.join(os.path.dirname(__file__), tube_config_info["tube_config_path"])
        if not os.path.exists(tube_config_path):
            logging.error(f"Tube config path does not exist: {tube_config_path}")
        img_save_time = config["save_days"]["days_to_keep"]
        if not isinstance(img_save_time, int) or img_save_time <= 0:
            logging.error(f"Invalid image save time: {img_save_time}, must be a positive integer.")
        img_save_location = os.path.join(os.path.dirname(__file__), config["save_days"]["image_folder_path"])
        if not os.path.exists(img_save_location):
            logging.warning(f"Image save location does not exist, creating: {img_save_location}")
            os.makedirs(img_save_location, exist_ok=True)
        logging.info(
            f"Server config details: IP:{ip}, Port:{port}, Camera Config:{camera_config_path}, Tube Config:{tube_config_path}, Save Days:{img_save_time}, Save Path:{img_save_location}")
        return ip, port, camera_config_path, tube_config_path, img_save_time, img_save_location
    except KeyError as e:
        raise KeyError(f"Missing key in config file: {e}")


def handle_command_camera(command, localizer):
    """检测相机是否连接成功，返回状态（0/1）"""
    try:
        logging.info("Checking camera status...")
        cap_success = localizer.camera_is_connected()
        logging.debug(f"Camera status: {cap_success}")
        return struct.pack("!i", cap_success)
    except Exception as e:
        logging.error(f"Error in handle_command_camera: {e}")
        return struct.pack("!i", -1)


def handle_command_localizer(command, localizer, save=False):
    """调用视觉定位的主函数，执行定位。返回包含 1 个状态码 + 6 个 float 位姿数据的结构体"""
    try:
        logging.info(f"Localizing with save={save}...")
        result = localizer.main_rectify_poses(save=save)
        logging.debug(f"Localizer result: {result}")
        return struct.pack("!i6f", *result)
    except Exception as e:
        logging.error(f"Error in handle_command_localizer: {e}")
        default_result = [-1, 9999.999, 9999.999, 9999.999, 9999.999, 9999.999, 9999.999]
        return struct.pack("!i6f", *default_result)


def display_basic(imgL, imgR, client_socket):
    send_command(imgL, imgR, client_socket)


def send_command(imgL, imgR, client_socket):
    _send_with_len(client_socket, imgL)
    _send_with_len(client_socket, imgR)


import struct


def _send_with_len(sock, img_pair):
    frameL, frameR = img_pair
    combined_img = cv2.hconcat([frameL, frameR])
    _, img_encoded = cv2.imencode('.jpg', combined_img)
    img_data = img_encoded.tobytes()
    size_data = struct.pack("!I", len(img_data))
    sock.sendall(size_data)
    sock.sendall(img_data)


def _send_image(sock, image):
    _, img_encoded = cv2.imencode(".jpg", image)
    data = img_encoded.tobytes()
    sock.sendall(struct.pack("!I", len(data)))
    sock.sendall(data)


def handle_command_send_image(localizer):
    try:
        frames = localizer.get_current_frame()  # [frameL, frameR]
        if frames is None or frames[0] is None or frames[1] is None:
            return struct.pack("!i", -1)
        frameL, frameR = frames
        combined_img = cv2.hconcat([frameL, frameR])
        _, img_encoded = cv2.imencode('.jpg', combined_img)
        img_data = img_encoded.tobytes()
        size_data = struct.pack("!I", len(img_data))
        logging.info(f"Sending combined image data of length: {len(img_data)} bytes")
        return size_data + img_data
    except Exception as e:
        logging.error(f"Error in handle_command_send_image: {e}")
        return struct.pack("!i", -1)


def client_handler(client_socket, address, localizer):
    """用于和上位机的命令交互"""
    global t0_stop_event, t0_thread, aging_active, q0_stop_event, q0_thread, s_stop_event, s_thread
    calibrator = TubeCalibrator(camera_params_path, model_path, config_path_1, out_folder,
                                tube_config_path_1, localizer.cap)
    try:
        camera_capture = ChessboardCaptureThread(localizer.cap)
        sock_file = client_socket.makefile('rb')
        while True:
            # command = client_socket.recv(1024).decode("utf-8").strip()
            # command = sock_file.readline().decode("utf-8").strip()
            # command_line = sock_file.readline()
            # if not command_line:
            #    break
            # command = command_line.decode('utf-8').strip()
            command = recv_line(client_socket)
            print(f"[Server] 接收到命令: {command}")
            if not command:
                break

            elif command == "status":
                client_socket.sendall(struct.pack("!i", 1))

            elif command == "Q_0":
                """用于连接，即只返回实时图像"""
                logging.info("进入Q_0实时图像流模式")
                q0_stop_event.clear()

                def q0_loop():
                    try:
                        last_time = time.time()
                        while not q0_stop_event.is_set():
                            elapsed = time.time() - last_time
                            if elapsed < 0.033:  # 33ms/帧
                                time.sleep(0.033 - elapsed)
                            frames = localizer.cap.capture()
                            last_time = time.time()
                            if frames and frames[0] is not None and frames[1] is not None:
                                frameL, frameR = frames
                                combined_img = cv2.hconcat([frameL, frameR])
                                _, img_encoded = cv2.imencode('.jpg', combined_img)
                                img_data = img_encoded.tobytes()
                                size_data = struct.pack("!I", len(img_data))
                                client_socket.sendall(size_data + img_data)
                            time.sleep(0.03)
                    except Exception as e:
                        logging.error(f"Q_0 实时图像流出错: {e}")
                    finally:
                        logging.info("Q_0 图像流线程退出")

                q0_thread = threading.Thread(target=q0_loop, daemon=True)
                q0_thread.start()

            elif command == "Q_stop":
                """停止实时显示线程"""
                if q0_thread and q0_thread.is_alive():
                    q0_stop_event.set()
                    q0_thread.join()
                    logging.info("Q_0 图像流线程已停止")
                client_socket.sendall(struct.pack("!i", 1))

            elif command == "Q_0_N":
                """棋盘格检测结果跳过"""
                camera_capture.resume()
                client_socket.sendall(struct.pack("!i", 1))
                logging.info("客户端跳过保存，继续采集")

            elif command == "Q_0_S":
                """棋盘格检测结果保存"""
                result = camera_capture.save_current_frame()
                camera_capture.resume()
                client_socket.sendall(struct.pack("!i", result))
                logging.info("已保存图像，发送路径给客户端")

            elif command == "S":
                """开始棋盘格检测"""
                logging.info("进入 S 图像流模式")
                s_stop_event.clear()

                def s_loop():
                    try:
                        camera_capture.start()
                        while not s_stop_event.is_set():
                            if camera_capture.paused_after_detection:
                                frames = camera_capture.get_latest_frames()
                            else:
                                frames = camera_capture.get_latest_raw_frame()
                            if frames:
                                combined_img = cv2.hconcat(frames)
                                _, img_encoded = cv2.imencode('.jpg', combined_img)
                                img_data = img_encoded.tobytes()
                                size_data = struct.pack("!I", len(img_data))
                                client_socket.sendall(size_data + img_data)
                            if camera_capture.paused_after_detection:
                                response = client_socket.recv(1024).decode("utf-8").strip()
                            else:
                                time.sleep(0.03)
                    except Exception as e:
                        logging.error(f"S 图像流出错: {e}")
                    finally:
                        camera_capture.stop()
                        logging.info("S 图像流线程退出")

                s_thread = threading.Thread(target=s_loop, daemon=True)
                s_thread.start()

            elif command == "S_stop":
                """停止棋盘格检测"""
                if s_thread and s_thread.is_alive():
                    s_stop_event.set()
                    s_thread.join()
                    logging.info("S 图像流线程已停止")
                client_socket.sendall(struct.pack("!i", 1))

            elif command == "C_0":
                """相机标定"""
                logging.warning(f"command: {command}")
                try:
                    root_path = "棋盘格图像"
                    camera_config_folder = "标定参数"
                    os.makedirs(camera_config_folder, exist_ok=True)
                    left_img_path = os.path.join(root_path, "左")
                    right_img_path = os.path.join(root_path, "右")
                    camera_config_path = os.path.join(camera_config_folder, "相机参数.pkl")
                    path, retS, T = calibrate_camera(left_img_path, right_img_path, camera_config_path,
                                            chessboard_size=(8, 6), square_size=25)
                    path_bytes = path.encode("utf-8")
                    T_bytes = struct.pack("!3d", *T.flatten())
                    retS_bytes = struct.pack("!d", retS)
                    response = struct.pack("!I", len(path_bytes)) + path_bytes + retS_bytes + T_bytes
                    client_socket.sendall(response)
                except Exception as e:
                    logging.error(f"相机标定失败: {e}")
                    client_socket.sendall(b"CALIBRATION_FAILED")
                continue

            elif command == "T_0":
                """导管标定"""
                logging.info("进入 T_0 导管标定实时模式")
                t0_stop_event.clear()

                def t0_loop():
                    try:
                        while not t0_stop_event.is_set():
                            result = calibrator.capture_and_process()
                            if result is None:
                                logging.error("capture_and_process() 返回 None，可能是相机未就绪或帧获取失败")
                                break
                            (left_vis, right_vis), point3d = result
                            if left_vis is not None and right_vis is not None:
                                combined_img = cv2.hconcat([left_vis, right_vis])
                                _, img_encoded = cv2.imencode('.jpg', combined_img)
                                img_data = img_encoded.tobytes()
                                if point3d is not None:
                                    print(f"3D点坐标: {point3d}")
                                    point3d_json = json.dumps({'point3d': list(point3d)})
                                else:
                                    point3d_json = json.dumps({'point3d': None})
                                point3d_bytes = point3d_json.encode('utf-8')
                                img_len = struct.pack("!I", len(img_data))
                                point_len = struct.pack("!I", len(point3d_bytes))
                                client_socket.sendall(img_len + img_data + point_len + point3d_bytes)
                            else:
                                logging.warning("未获取到图像数据")
                            time.sleep(0.02)
                    except Exception as e:
                        logging.error(f"T_0 处理异常: {e}")
                    finally:
                        logging.warning("T_0 模式已退出")

                t0_thread = threading.Thread(target=t0_loop, daemon=True)
                t0_thread.start()

            elif command == "S_M_Y":
                """加载视觉模型"""
                model_path_bytes = model_path.encode("utf-8")
                response = struct.pack("!I", len(model_path_bytes))
                response += model_path_bytes
                client_socket.sendall(response)

            elif command == "C_M":
                """加载相机参数"""
                try:
                    model_path_bytes = camera_params_path.encode("utf-8")
                    response = struct.pack("!I", len(model_path_bytes))
                    response += model_path_bytes
                    client_socket.sendall(response)
                except Exception as e:
                    logging.error(f"相机参数加载失败: {e}")
                    client_socket.sendall(struct.pack("!i", 0))

            elif command == "T_0_S":
                """保存导管标定参数，并退出"""
                if t0_thread and t0_thread.is_alive():
                    t0_stop_event.set()
                    t0_thread.join()
                    logging.info("T_0 模式线程已停止")
                    key_folder = r'new_config/tube_config.pkl'
                    out_path = r'configs/path'
                    os.makedirs(out_path, exist_ok=True)
                    dst_path = os.path.join(out_path, 'tube_config.pkl')
                    try:
                        shutil.copy2(key_folder, dst_path)
                        logging.info(f"配置文件已复制到 {dst_path}")
                    except Exception as e:
                        logging.error(f"复制 tube_config.pkl 失败: {e}")
                    result = dst_path
                    model_path_bytes = result.encode("utf-8")
                    response = struct.pack("!I", len(model_path_bytes))
                    response += model_path_bytes
                    client_socket.sendall(response)

            elif command == "Y_VAL":
                """更新Y轴坐标"""
                logging.warning(f"XXXXX[Server] 接收到命令: '{command}'")
                try:
                    # y_bytes = client_socket.recv(4)
                    y_bytes = recv_exact(client_socket, 4)
                    if len(y_bytes) < 4:
                        raise ValueError("Y坐标字节不足")
                    y_value = struct.unpack("!i", y_bytes)[0]
                    logging.info(f"[Server] 接收到 Y 值: {y_value}")
                    result = calibrator.process_y_value(y_value)
                    status_code = 1 if result else 0
                    client_socket.sendall(struct.pack("!i", status_code))
                except Exception as e:
                    logging.error(f"处理 Y_VAL 命令失败: {e}")
                    client_socket.sendall(struct.pack("!i", 0))

            elif command == "P_0":
                """偏移量计算"""
                command_id = "3"
                tube_config_path = "./configs/tube_config_6.pkl"
                try:
                    config_file_path = os.path.join(os.path.dirname(__file__), "configs/config.json")
                    ip, port, camera_config_path, _, img_save_time, img_save_location = read_server_config(
                        config_file_path, part_id="6")
                    localizer.update_tube_config(tube_config_path)
                    logging.info(f"Tube config updated to: {tube_config_path}")
                    response = handle_command_localizer(command_id, localizer, save=True)
                except Exception as e:
                    logging.error(f"Error handling Q_0 command: {e}")
                    response = struct.pack("!i", -1)
                client_socket.sendall(response)
            else:
                logging.warning(f"[OrangePi] 未处理的命令: {command}")
    except Exception as e:
        logging.error(f"Error in client_handler: {e}")
        client_socket.close()
        logging.info(f"Client disconnected: {address}")
    finally:
        for event, thread in [(q0_stop_event, q0_thread),
                              (t0_stop_event, t0_thread),
                              (s_stop_event, s_thread)]:
            if thread and thread.is_alive():
                event.set()
                thread.join(timeout=1.0)
        client_socket.close()


def recv_exact(sock, size):
    data = b''
    while len(data) < size:
        part = sock.recv(size - len(data))
        logging.warning(f"[DEBUG] 收到 {len(part)} 字节: {part.hex()}")
        if not part:
            raise ConnectionError("连接关闭或无数据")
        data += part
    return data


def recv_line(sock):
    data = b""
    while not data.endswith(b"\n"):
        part = sock.recv(1)
        if not part:
            break
        data += part
    return data.decode().strip()


def delete_old_images(img_save_time, img_save_location):
    """用于定期删除超出保存天数的旧图片，防止磁盘爆满"""
    current_time = time.time()
    for filename in os.listdir(img_save_location):
        file_path = os.path.join(img_save_location, filename)
        if os.path.isfile(file_path) and filename.lower().endswith((".jpg", ".jpeg", ".png", ".bmp", ".gif")):
            file_mtime = os.path.getmtime(file_path)
            file_age_days = (current_time - file_mtime) / (60 * 60 * 24)
            if file_age_days > img_save_time:
                logging.info(f"Deleting file: {file_path} (Age: {int(file_age_days)} days)")
                os.remove(file_path)


def delete_images_periodically(img_save_time, img_save_location, interval=86400):
    """用于定期删除超出保存天数的旧图片，防止磁盘爆满"""
    delete_old_images(img_save_time, img_save_location)
    threading.Timer(interval, delete_images_periodically, args=(img_save_time, img_save_location, interval)).start()


def server_main():
    """
    初始化本地视觉定位器 VisionLocalizer
        启动摄像头
        启动 socket 监听客户端连接
        为每个客户端开线程调用 client_handler
        异常退出时释放资源
    """
    logging.info("Starting server...")
    print("Starting server...")
    config_file_path = os.path.join(os.path.dirname(__file__), "configs/config.json")
    part_id = "6"
    try:
        ip, port, camera_config_path, tube_config_path, img_save_time, img_save_location = read_server_config(
            config_file_path, part_id)
    except ValueError as e:
        logging.warning(f"Configuration warning: {e}")
        return
    logging.info("Initializing localizer...")
    print("Starting server...")
    picture_save_path = os.path.join(os.path.dirname(__file__), "saved_pictures")
    logging.info(f"open {picture_save_path}")
    localizer = VisionLocalizer(camera_config_path, tube_config_path, picture_save_path)
    logging.info(f"Server localizer on {localizer}")
    print("1234")
    # localizer.cap.start()

    delete_images_periodically(img_save_time, img_save_location)

    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    server_socket.bind((ip, port))
    server_socket.listen(1)
    logging.info(f"Server listening on {ip}:{port}")
    try:
        while True:
            client_socket, address = server_socket.accept()
            client_thread = threading.Thread(target=client_handler, args=(client_socket, address, localizer))
            client_thread.start()
    except KeyboardInterrupt:
        logging.warning("KeyboardInterrupt, shutting down server...")
        # finally:
        #     if localizer.cap.camera_is_connected() == 0:
        #         logging.warning("Releasing camera...")
        #         localizer.cap.release_camera()
        #     localizer.cap.join()
        server_socket.close()


class TextHandler(std_logging.Handler):
    """用于将日志写入 GUI 的 ScrolledText 文本区域，实现实时日志显示"""

    def __init__(self, text_widget):
        super().__init__()
        self.text_widget = text_widget

    def emit(self, record):
        msg = self.format(record)

        def append():
            self.text_widget.configure(state='normal')
            self.text_widget.insert(tk.END, msg + '\n')
            self.text_widget.configure(state='disabled')
            self.text_widget.yview(tk.END)

        self.text_widget.after(0, append)


shutdown_event = Event()
server_socket_global = None


def start_server():
    """在界面加载后调用，启动服务线程（调用 server_main_wrapper）"""
    global server_socket_global
    shutdown_event.clear()
    server_thread = threading.Thread(target=server_main_wrapper, daemon=True)
    server_thread.start()
    log_text.insert(tk.END, "服务器启动中...\n")


def server_main_wrapper():
    """包装了 server_main，用于异常捕获与线程调用"""
    global server_socket_global
    try:
        server_main()
    except Exception as e:
        logging.error(f"服务器异常: {e}")
    finally:
        server_socket_global = None


def shutdown_server():
    """
    点击“关闭服务”按钮后调用：
        设置关闭标志
        关闭 socket
        关闭主界面
    """
    shutdown_event.set()
    global server_socket_global
    if server_socket_global:
        try:
            server_socket_global.close()
            log_text.insert(tk.END, "正在关闭服务器连接...\n")
        except Exception as e:
            logging.error(f"关闭服务器异常: {e}")
    else:
        log_text.insert(tk.END, "服务器未运行\n")
    root.after(1000, root.destroy)


if __name__ == "__main__":
    import tkinter as tk
    from tkinter.scrolledtext import ScrolledText

    root = tk.Tk()
    root.title("视觉定位服务器控制台")
    root.geometry("1200x800")
    root.configure(bg="#2e2e2e")

    log_text = ScrolledText(root,
                            state='disabled',
                            bg="black",
                            fg="#00FF00",
                            font=("Consolas", 12),
                            padx=10,
                            pady=10)
    log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    control_frame = tk.Frame(root, bg="#2e2e2e")
    control_frame.pack(fill=tk.X, padx=10, pady=5)

    btn_style = {
        "font": ("Arial", 12),
        "width": 15,
        "relief": tk.GROOVE,
        "bd": 2
    }

    shutdown_btn = tk.Button(control_frame,
                             text="关闭服务",
                             command=shutdown_server,
                             bg="#F44336",
                             fg="white",
                             **btn_style)
    shutdown_btn.pack(side=tk.RIGHT, padx=5)

    ui_handler = TextHandler(log_text)
    formatter = std_logging.Formatter("%(asctime)s [%(levelname)s] %(message)s")
    ui_handler.setFormatter(formatter)
    std_logging.getLogger().addHandler(ui_handler)

    root.after(100, start_server)

    root.mainloop()
