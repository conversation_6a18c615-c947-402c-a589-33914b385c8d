#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的视频处理脚本 - 解决1KB问题
"""

import cv2
import base64
import requests
import json
import os

def getByte(path):
    """将图片文件转换为base64字符串"""
    with open(path, 'rb') as f:
        img_byte = base64.b64encode(f.read())
    img_str = img_byte.decode('ascii')
    return img_str

def process_video():
    """处理视频的主函数"""
    
    # 输入输出路径
    input_video_path = "video.mp4"
    output_video_path = "output_fixed.avi"  # 使用avi格式更稳定
    
    # 检查输入文件
    if not os.path.exists(input_video_path):
        print(f"❌ 输入视频不存在: {input_video_path}")
        return False
    
    # 打开输入视频
    cap = cv2.VideoCapture(input_video_path)
    if not cap.isOpened():
        print("❌ 无法打开输入视频")
        return False
    
    # 获取视频属性
    original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"📹 输入视频信息:")
    print(f"   原始分辨率: {original_width} x {original_height}")
    print(f"   帧率: {fps} FPS")
    print(f"   总帧数: {total_frames}")
    
    # 计算裁剪后的尺寸
    crop_width = 1920 - 600  # 1320
    crop_height = original_height
    
    print(f"   裁剪后分辨率: {crop_width} x {crop_height}")
    
    # 创建视频写入器 - 使用XVID编码和AVI格式
    fourcc = cv2.VideoWriter_fourcc(*'XVID')
    out = cv2.VideoWriter(output_video_path, fourcc, fps, (crop_width, crop_height), isColor=True)
    
    if not out.isOpened():
        print("❌ 无法创建视频写入器")
        cap.release()
        return False
    
    print("✅ 视频写入器创建成功")
    
    frame_count = 0
    processed_count = 0
    
    try:
        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                print(f"\n视频读取完成")
                break
            
            frame_count += 1
            print(f"处理第 {frame_count} 帧", end=' ')
            
            # 裁剪图像
            cropped_frame = frame[:, 600:1920]
            
            # 验证裁剪后的尺寸
            actual_height, actual_width = cropped_frame.shape[:2]
            if actual_width != crop_width or actual_height != crop_height:
                print(f"尺寸不匹配，调整: {actual_width}x{actual_height} -> {crop_width}x{crop_height}")
                cropped_frame = cv2.resize(cropped_frame, (crop_width, crop_height))
            
            # 保存临时图像用于API调用
            temp_path = 'temp_frame.jpg'
            cv2.imwrite(temp_path, cropped_frame)
            
            # 模拟API调用（如果API不可用，跳过）
            processed_frame = cropped_frame.copy()
            
            try:
                # 调用API
                img_str = getByte(temp_path)
                json_str = json.dumps({'image': img_str})
                
                response = requests.post('http://192.168.2.3:8878/mmr', json_str, timeout=5)
                
                if response.status_code == 200:
                    data = json.loads(response.text)
                    
                    # 绘制检测框
                    if 'Result' in data and 'rect' in data['Result']:
                        boxes = data['Result']['rect']
                        
                        for idx, box in enumerate(boxes, start=1):
                            try:
                                y1, y2, x1, x2 = map(int, box)
                                
                                # 边界检查
                                if (0 <= x1 < x2 <= crop_width and 0 <= y1 < y2 <= crop_height):
                                    cv2.rectangle(processed_frame, (x1, y1), (x2, y2), (0, 0, 255), 3)
                                    cv2.putText(processed_frame, f"Obj{idx}", (x1, y1-10), 
                                              cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                                
                            except Exception as e:
                                print(f"绘制框异常: {e}")
                    
                    print("API✅", end=' ')
                else:
                    print("API❌", end=' ')
                    
            except Exception as e:
                print(f"API异常: {e}", end=' ')
            
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)
            
            # 写入视频帧
            out.write(processed_frame)
            processed_count += 1
            print("写入✅")
            
            # 限制处理帧数（测试用）
            if frame_count >= fps:  # 只处理1秒
                print(f"已处理 {frame_count} 帧，停止")
                break
                
    except Exception as e:
        print(f"处理异常: {e}")
    
    finally:
        # 释放资源
        cap.release()
        out.release()
    
    # 验证输出文件
    print(f"\n📊 处理结果:")
    print(f"   处理帧数: {frame_count}")
    print(f"   写入帧数: {processed_count}")
    
    if os.path.exists(output_video_path):
        file_size = os.path.getsize(output_video_path)
        print(f"   输出文件: {output_video_path}")
        print(f"   文件大小: {file_size / (1024*1024):.2f} MB")
        
        if file_size > 10240:  # 大于10KB
            print("✅ 文件大小正常")
            
            # 验证可读性
            test_cap = cv2.VideoCapture(output_video_path)
            if test_cap.isOpened():
                ret, test_frame = test_cap.read()
                if ret:
                    print("✅ 视频可以正常播放")
                    print(f"   实际分辨率: {test_frame.shape[1]} x {test_frame.shape[0]}")
                    
                    # 获取视频信息
                    test_frames = int(test_cap.get(cv2.CAP_PROP_FRAME_COUNT))
                    test_fps = int(test_cap.get(cv2.CAP_PROP_FPS))
                    print(f"   视频帧数: {test_frames}")
                    print(f"   视频帧率: {test_fps}")
                    
                else:
                    print("❌ 无法读取视频帧")
                test_cap.release()
            else:
                print("❌ 无法打开输出视频")
        else:
            print("❌ 文件过小，可能写入失败")
            return False
    else:
        print("❌ 输出文件不存在")
        return False
    
    return True

def main():
    print("🎥 简化视频处理脚本")
    print("=" * 40)
    
    success = process_video()
    
    if success:
        print("\n🎉 视频处理成功完成!")
        print("💡 如果这个脚本工作正常，说明问题已解决")
    else:
        print("\n❌ 视频处理失败")
        print("💡 请检查输入文件和网络连接")

if __name__ == "__main__":
    main()
