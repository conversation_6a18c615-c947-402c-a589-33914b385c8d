��\      }�(�left_cx�Me�left_cy�M,�left_tube_contour��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KMMKK��h�dtype����i4�����R�(K�<�NNNJ����J����K t�b�Bh
  e    d    c    b    a    `    _    ^    ]    \    \    [    [  	  [  
  Z    Z    Z  
  Z    Z    Z    Z    [    [    [    [    [    [    [    [    [    [    [    [    [    [    [     [  !  [  "  [  #  [  $  [  %  [  &  [  '  \  (  \  )  \  *  \  +  \  ,  \  -  \  .  \  /  \  0  \  1  \  2  \  3  \  4  \  5  \  6  \  7  \  8  \  9  \  :  \  ;  \  <  \  =  \  >  \  ?  \  @  \  A  \  B  \  C  \  D  \  E  ]  F  ]  G  ]  H  ]  I  ]  J  ]  K  ]  L  ]  M  ]  N  ]  O  ]  P  ]  Q  ]  R  ]  S  ]  T  ]  U  ]  V  ]  W  ]  X  ]  Y  ]  Z  ]  [  ]  \  ]  ]  ]  ^  ]  _  ]  `  ]  a  ]  b  ]  c  ]  d  ^  e  ^  f  ^  g  ^  h  ^  i  ^  j  ^  k  ^  l  ^  m  ^  n  ^  o  ^  p  ^  q  ]  r  ]  s  ]  t  ]  u  ]  v  ]  w  ]  x  ]  y  ]  z  ]  {  ]  |  ]  }  ]  ~  ]    ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  ^  �  _  �  _  �  _  �  _  �  `  �  `  �  a  �  b  �  c  �  d  �  e  �  f  �  g  �  h  �  i  �  j  �  j  �  k  �  k  �  l  �  l  �  l  �  l  �  m  �  m  �  m  �  m  �  m  �  m  �  m  �  m  �  m  �  m  �  m  �  m  �  m  �  m  �  m  �  n  �  n  �  n  �  n  �  n  �  o  �  o  �  o  �  o  �  o  �  o    o  ~  o  }  p  |  p  {  p  z  p  y  p  x  p  w  p  v  p  u  p  t  q  s  q  r  q  q  q  p  q  o  q  n  q  m  q  l  q  k  q  j  q  i  q  h  q  g  q  f  q  e  q  d  q  c  q  b  q  a  q  `  p  _  p  ^  p  ]  p  \  p  [  p  Z  p  Y  q  X  q  W  q  V  q  U  q  T  q  S  q  R  q  Q  q  P  q  O  q  N  q  M  q  L  p  K  p  J  p  I  p  H  p  G  p  F  p  E  p  D  p  C  p  B  p  A  o  @  o  ?  o  >  o  =  o  <  o  ;  o  :  o  9  n  8  n  7  n  6  n  5  n  4  n  3  n  2  n  1  n  0  n  /  n  .  n  -  m  ,  m  +  m  *  m  )  m  (  m  '  m  &  m  %  m  $  m  #  m  "  m  !  m     m    m    m    m    m    m    m    m    m    l    l    l    l    l    l    l    k    k    k  
  k    k    k  
  l  	  k    k    k    j    i    h    g    f    �t�b�right_cx�K��right_cy�M,�right_tube_contour�hh	K ��h��R�(KM�KK��h�Bh  �   �   �   �   �   �   �   �   �   �   �   �   �      �     �     �     �     �     �     �     �     �     �     �     �     �   	  �   
  �     �     �   
  �     �     �     �     �     �     �     �     �     �     �     �     �     �     �     �     �     �     �      �   !  �   "  �   #  �   $  �   %  �   &  �   '  �   (  �   )  �   *  �   +  �   ,  �   -  �   .  �   /  �   0  �   1  �   2  �   3  �   4  �   5  �   6  �   7  �   8  �   9  �   :  �   ;  �   <  �   =  �   >  �   ?  �   @  �   A  �   B  �   C  �   D  �   E  �   F  �   G  �   H  �   I  �   J  �   K  �   L  �   M  �   N  �   O  �   P  �   Q  �   R  �   S  �   T  �   U  �   V  �   W  �   X  �   Y  �   Z  �   [  �   \  �   ]  �   ^  �   _  �   `  �   a  �   b  �   c  �   d  �   e  �   f  �   g  �   h  �   i  �   j  �   k  �   l  �   m  �   n  �   o  �   p  �   q  �   r  �   s  �   t  �   u  �   v  �   w  �   x  �   y  �   z  �   {  �   |  �   }  �   ~  �     �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �     �   ~  �   }  �   |  �   {  �   z  �   y  �   x  �   w  �   v  �   u  �   t  �   s  �   r  �   q  �   p  �   o  �   n  �   m  �   l  �   k  �   j  �   i  �   h  �   g  �   f  �   e  �   d  �   c  �   b  �   a  �   `  �   _  �   ^  �   ]  �   \  �   [  �   Z  �   Y  �   X  �   W  �   V  �   U  �   T  �   S  �   R  �   Q  �   P  �   O  �   N  �   M  �   L  �   K  �   J  �   I  �   H  �   G  �   F  �   E  �   D  �   C  �   B  �   A  �   @  �   ?  �   >  �   =  �   <  �   ;  �   :  �   9  �   8  �   7  �   6  �   5  �   4  �   3  �   2  �   1  �   0  �   /  �   .  �   -  �   ,  �   +  �   *  �   )  �   (  �   '  �   &  �   %  �   $  �   #  �   "  �   !  �      �     �     �     �     �     �     �     �     �     �     �     �     �     �     �     �     �     �     �   
  �     �     �   
  �   	  �     �     �     �     �     �     �     �     �      �   �   �   �   �   �   �t�b�point3d�]�(h�scalar���h�f8�����R�(KhNNNJ����J����K t�bCaag%m+@���R�h$h'C��g@�>@���R�h$h'C
�����d@���R�eu.