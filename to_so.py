import os
import subprocess
from Cython.Build import cythonize


def convert_to_so(path_list):
    # 遍历所有目录路径，查找 Python 文件进行转换
    for path in path_list:
        for root, dirs, files in os.walk(path):
            for file in files:
                if file.endswith('.py'):
                    py_file_path = os.path.join(root, file)
                    # 转换文件到 .c
                    c_file_path = py_file_path.replace('.py', '.c')
                    subprocess.run(['cython', py_file_path, '-o', c_file_path])

                    # 创建适用于 setup.py 的 Python 配置
                    setup_content = f"""
from setuptools import setup, Extension
from Cython.Build import cythonize

ext_modules = [
    Extension(
        name="{file.replace('.py', '')}",
        sources=["{c_file_path}"],
    )
]

setup(
    name="{file.replace('.py', '')}",
    ext_modules=cythonize(ext_modules, compiler_directives={{'language_level': 3}}),
)
"""
                    # 将生成的 setup 脚本保存到文件
                    setup_path = os.path.join(root, 'setup.py')
                    with open(setup_path, 'w') as setup_file:
                        setup_file.write(setup_content)

                    # 编译 .c 文件为 .so
                    subprocess.run(['python3', setup_path, 'build_ext', '--inplace'])

                    # 删除生成的 .c 文件和 setup.py 以保持干净
                    os.remove(c_file_path)
                    os.remove(setup_path)

                    print(f'{file} converted to .so successfully.')


if __name__ == '__main__':
    # Define your paths
    path_list = ['./', './backup', './server_utils']

    # Run the conversion
    convert_to_so(path_list)
