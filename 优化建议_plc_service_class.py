#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PLC通信服务类 - 优化版本
将全局变量封装成类，提高代码的可维护性和扩展性
"""

import socket
import struct
import threading
import json
from enum import Enum
from typing import Optional, Callable
from server_utils.logger_config import logger as logging

class PLCMode(Enum):
    UNKNOWN = "unknown"
    MANUAL = "manual"
    AUTO = "auto"

class AutoState(Enum):
    IDLE = "idle"
    PREPARING = "preparing"
    READY = "ready"
    PLC_READY = "plc_ready"
    WAITING_POSITION = "waiting_position"
    WAITING_JUDGMENT = "waiting_judgment"
    JUDGING = "judging"
    RESULT_SENT = "result_sent"

class ManualState(Enum):
    IDLE = "idle"
    PREPARING = "preparing"
    READY = "ready"
    PLC_READY = "plc_ready"
    CLAMPING = "clamping"

class PLCService:
    """PLC通信服务类"""
    
    def __init__(self, host: str = "0.0.0.0", port: int = 2000, vision_callback: Optional[Callable] = None):
        self.host = host
        self.port = port
        self.vision_callback = vision_callback
        
        # 状态管理
        self._state_lock = threading.RLock()
        self._current_mode = PLCMode.UNKNOWN
        self._manual_state = ManualState.IDLE
        self._auto_state = AutoState.IDLE
        self._heartbeat_counter = 0
        self._last_judgment_result = None
        
        # 网络相关
        self._server_socket: Optional[socket.socket] = None
        self._running = False
        self._server_thread: Optional[threading.Thread] = None
        self._connections = []
        
        # 回调函数
        self._state_callbacks = {}
        
    def start(self):
        """启动PLC服务"""
        if self._running:
            logging.warning("PLC服务已在运行")
            return
        
        try:
            self._server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self._server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self._server_socket.bind((self.host, self.port))
            self._server_socket.listen(5)
            
            self._running = True
            self._server_thread = threading.Thread(target=self._server_loop, daemon=True)
            self._server_thread.start()
            
            logging.info(f"[PLC] 服务已启动，监听 {self.host}:{self.port}")
            
        except Exception as e:
            logging.error(f"启动PLC服务失败: {e}")
            self.stop()
    
    def stop(self):
        """停止PLC服务"""
        self._running = False
        
        # 关闭所有连接
        for conn in self._connections[:]:
            try:
                conn.close()
            except:
                pass
        self._connections.clear()
        
        # 关闭服务器socket
        if self._server_socket:
            try:
                self._server_socket.close()
            except:
                pass
            self._server_socket = None
        
        logging.info("[PLC] 服务已停止")
    
    def register_state_callback(self, event: str, callback: Callable):
        """注册状态变化回调"""
        if event not in self._state_callbacks:
            self._state_callbacks[event] = []
        self._state_callbacks[event].append(callback)
    
    def get_current_state(self) -> dict:
        """获取当前状态"""
        with self._state_lock:
            return {
                "mode": self._current_mode.value,
                "manual_state": self._manual_state.value,
                "auto_state": self._auto_state.value,
                "heartbeat": self._heartbeat_counter,
                "last_result": self._last_judgment_result
            }
    
    def _server_loop(self):
        """服务器主循环"""
        while self._running:
            try:
                conn, addr = self._server_socket.accept()
                logging.info(f"[PLC] 连接来自: {addr}")
                
                self._connections.append(conn)
                client_thread = threading.Thread(
                    target=self._handle_client,
                    args=(conn, addr),
                    daemon=True
                )
                client_thread.start()
                
            except Exception as e:
                if self._running:
                    logging.error(f"接受PLC连接失败: {e}")
    
    def _handle_client(self, conn: socket.socket, addr):
        """处理PLC客户端连接"""
        try:
            while self._running:
                # 接收PLC数据
                data = self._recv_exact(conn, 48)
                if not data:
                    break
                
                # 解析并处理命令
                parsed_data = self._parse_plc_data(data)
                response_data = self._process_command(parsed_data)
                
                # 发送响应
                conn.sendall(response_data)
                
        except Exception as e:
            logging.error(f"处理PLC连接异常: {e}")
        finally:
            self._cleanup_connection(conn, addr)
    
    def _cleanup_connection(self, conn: socket.socket, addr):
        """清理连接"""
        try:
            conn.close()
            if conn in self._connections:
                self._connections.remove(conn)
        except:
            pass
        
        # 重置状态
        with self._state_lock:
            self._current_mode = PLCMode.UNKNOWN
            self._manual_state = ManualState.IDLE
            self._auto_state = AutoState.IDLE
            
        logging.info(f"[PLC] 连接已断开: {addr}")
        self._notify_callbacks("connection_lost", addr)
    
    def _recv_exact(self, sock: socket.socket, size: int) -> bytes:
        """精确接收指定字节数"""
        data = b''
        while len(data) < size:
            chunk = sock.recv(size - len(data))
            if not chunk:
                raise ConnectionError("连接已断开")
            data += chunk
        return data
    
    def _parse_plc_data(self, data: bytes) -> dict:
        """解析PLC数据"""
        if len(data) != 48:
            raise ValueError("数据长度错误，应为48字节")
        
        pos_floats = struct.unpack("!6f", data[:24])
        controls = struct.unpack("!18B", data[24:42])
        
        # 解析控制位
        heartbeat = controls[0]
        manual_flag = controls[1]
        auto_flag = controls[2]
        
        # 确定模式
        if manual_flag == 255 and auto_flag == 255:
            mode = PLCMode.MANUAL  # 同时为255时优先手动模式
        elif manual_flag == 255:
            mode = PLCMode.MANUAL
        elif auto_flag == 255:
            mode = PLCMode.AUTO
        else:
            mode = PLCMode.UNKNOWN
        
        flags = {
            "plc_ready_clamp": controls[4] == 255,
            "not_in_position": controls[5] == 255,
            "in_position": controls[6] == 255,
            "start_judgment": controls[7] == 255,
            "result_received": controls[11] == 255,
        }
        
        return {
            "position": pos_floats,
            "heartbeat": heartbeat,
            "mode": mode,
            "manual_flag": manual_flag,
            "auto_flag": auto_flag,
            "flags": flags,
            "raw_bytes": data
        }
    
    def _process_command(self, parsed_data: dict) -> bytes:
        """处理PLC命令并生成响应"""
        with self._state_lock:
            # 更新状态
            self._current_mode = parsed_data["mode"]
            self._heartbeat_counter = (self._heartbeat_counter + 1) % 256
            
            # 根据模式处理
            if self._current_mode == PLCMode.AUTO:
                response_flags = self._handle_auto_mode(parsed_data)
            elif self._current_mode == PLCMode.MANUAL:
                response_flags = self._handle_manual_mode(parsed_data)
            else:
                response_flags = self._create_default_response()
            
            # 打包响应
            current_pos = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]  # 默认坐标
            return self._pack_response(current_pos, response_flags)
    
    def _handle_auto_mode(self, parsed_data: dict) -> dict:
        """处理自动模式"""
        flags = parsed_data["flags"]
        
        if self._auto_state == AutoState.IDLE:
            self._auto_state = AutoState.PREPARING
            return {"auto": True, "preparing": 9}
        
        elif self._auto_state == AutoState.PREPARING:
            self._auto_state = AutoState.READY
            return {"auto": True, "ready": 10}
        
        elif self._auto_state == AutoState.READY:
            if flags.get("plc_ready_clamp"):
                self._auto_state = AutoState.PLC_READY
                return {"auto": True, "clamp_ack": 20}
            return {"auto": True, "ready": 10}
        
        # ... 其他状态处理
        
        return {"auto": True}
    
    def _handle_manual_mode(self, parsed_data: dict) -> dict:
        """处理手动模式"""
        # 手动模式处理逻辑
        return {"manual": True}
    
    def _create_default_response(self) -> dict:
        """创建默认响应"""
        return {"manual": False, "auto": False}
    
    def _pack_response(self, pos: list, flags: dict) -> bytes:
        """打包响应数据"""
        pos_bytes = struct.pack("!6f", *pos)
        
        control_list = [
            self._heartbeat_counter,                       # 25 心跳
            255 if flags.get("manual", False) else 0,     # 26 手动
            255 if flags.get("auto", False) else 0,       # 27 自动
            flags.get("preparing", 0),                     # 28 准备
            flags.get("ready", 0),                         # 29 就绪
            flags.get("clamp_ack", 0),                     # 30 入夹确认
            flags.get("judging", 0),                       # 31 判定中
            flags.get("judgment_complete", 0),             # 32 判定完成
            0, 0, 0, 0, 0, 0, 0,                          # 33-39 预留
            255 if flags.get("result_ng", False) else 0,  # 40 NG结果
            255 if flags.get("result_ok", False) else 0,  # 41 OK结果
            0                                              # 42 预留
        ]
        
        full_bytes = pos_bytes + bytes(control_list) + bytes(48 - 24 - len(control_list))
        return full_bytes
    
    def _notify_callbacks(self, event: str, *args):
        """通知回调函数"""
        if event in self._state_callbacks:
            for callback in self._state_callbacks[event]:
                try:
                    callback(*args)
                except Exception as e:
                    logging.error(f"回调函数执行失败: {e}")

# 使用示例
def create_plc_service(vision_callback=None):
    """创建PLC服务实例"""
    return PLCService(vision_callback=vision_callback)
