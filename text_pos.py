import socket
import struct

def deg_to_rad(deg):
    return deg / 180 * 3.1415926

def main():
    server_ip = "************"
    server_port = 2000

    tcp_pos_deg = [500.0, 100.0, 200.0, 90.0, 0.0, 0.0]  # 位置+角度（角度将被 orangepi 转成弧度）

    # 打包为 6 个 double（48 字节）向 orangepi 发送
    data = struct.pack("!6d", *tcp_pos_deg)

    try:
        with socket.create_connection((server_ip, server_port), timeout=5) as sock:
            print(f"[Client] 连接服务器 {server_ip}:{server_port} 成功，发送坐标：")
            print(f"[Client] 原始角度坐标: {tcp_pos_deg}")

            sock.sendall(data)

            # 接收服务器返回的 6 个 double，弧度坐标
            recv_data = sock.recv(48)
            if len(recv_data) != 48:
                print("[Client] 接收数据长度异常！")
                return

            robot_target_pos_rad = struct.unpack("!6d", recv_data)
            print(f"[Client] 收到目标坐标（弧度）: {robot_target_pos_rad}")

    except Exception as e:
        print(f"[Client] 连接或通信失败: {e}")

if __name__ == "__main__":
    main()
