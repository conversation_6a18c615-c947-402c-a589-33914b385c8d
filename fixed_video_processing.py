#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复后的视频处理脚本
解决视频保存无法播放的问题
"""

import cv2
import base64
import requests
import json
import numpy as np
import os

def getByte(path):
    """将图片文件转换为base64字符串"""
    with open(path, 'rb') as f:
        img_byte = base64.b64encode(f.read())
    img_str = img_byte.decode('ascii')
    return img_str

def process_video_with_api():
    """处理视频并调用API进行检测"""
    
    # 指定本地视频路径和输出路径
    input_video_path = "video.mp4"       # 输入视频路径
    output_video_path = "processed_output.mp4"  # 输出处理后的视频路径
    
    # 打开输入视频文件
    cap = cv2.VideoCapture(input_video_path)
    
    # 检查视频是否成功打开
    if not cap.isOpened():
        print("❌ 错误: 无法打开视频文件")
        return False
    
    # 获取原视频的属性
    frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"📹 视频信息:")
    print(f"   分辨率: {frame_width} x {frame_height}")
    print(f"   帧率: {fps} FPS")
    print(f"   总帧数: {total_frames}")
    
    # 🔧 修复1: 创建彩色视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # 使用mp4v编码
    out = cv2.VideoWriter(output_video_path, fourcc, fps, (frame_width, frame_height), isColor=True)
    
    # 检查VideoWriter是否成功创建
    if not out.isOpened():
        print("❌ 错误: 无法创建输出视频文件")
        cap.release()
        return False
    
    print(f"✅ 成功创建输出视频: {output_video_path}")
    
    frame_count = 0
    processed_count = 0
    
    # 逐帧处理视频
    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            print("📄 视频读取完成")
            break
        
        frame_count += 1
        print(f"🔄 处理第 {frame_count}/{total_frames} 帧", end='\r')
        
        try:
            # 保存当前帧为临时图片
            temp_image_path = 'temp_frame.jpg'
            cv2.imwrite(temp_image_path, frame)
            
            # 🔧 修复2: 确保图像尺寸正确
            processed_frame = frame.copy()
            
            # 调用API进行检测
            img_str = getByte(temp_image_path)
            json_str = json.dumps({'image': img_str})
            
            try:
                # 发送API请求
                response = requests.post('http://192.168.2.3:8878/mmr', json_str, timeout=10)
                
                if response.status_code == 200:
                    data = json.loads(response.text)
                    
                    # 绘制检测框
                    if 'Result' in data and 'rect' in data['Result']:
                        boxes = data['Result']['rect']
                        
                        for idx, box in enumerate(boxes, start=1):
                            try:
                                y1, y2, x1, x2 = map(int, box)
                                
                                # 验证坐标有效性
                                if not (0 <= x1 < x2 <= frame_width and 0 <= y1 < y2 <= frame_height):
                                    print(f"\n⚠️  无效坐标: ({x1},{y1}) - ({x2},{y2})")
                                    continue
                                
                                # 绘制矩形框
                                cv2.rectangle(processed_frame, (x1, y1), (x2, y2), (0, 0, 255), 3)
                                
                                # 添加标签
                                label = f"Object {idx}"
                                cv2.putText(processed_frame, label, (x1, y1-10), 
                                          cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                                
                            except Exception as box_error:
                                print(f"\n❌ 矩形框处理异常: {box_error}")
                    
                    processed_count += 1
                    
                else:
                    print(f"\n⚠️  API请求失败: {response.status_code}")
                    
            except requests.exceptions.RequestException as api_error:
                print(f"\n❌ API调用异常: {api_error}")
            
            # 🔧 修复3: 确保图像格式和尺寸正确
            if processed_frame.shape[:2] != (frame_height, frame_width):
                processed_frame = cv2.resize(processed_frame, (frame_width, frame_height))
            
            # 写入处理后的帧
            out.write(processed_frame)
            
            # 清理临时文件
            if os.path.exists(temp_image_path):
                os.remove(temp_image_path)
                
        except Exception as frame_error:
            print(f"\n❌ 帧处理异常: {frame_error}")
            # 如果处理失败，写入原始帧
            out.write(frame)
        
        # 限制处理帧数（用于测试）
        if frame_count >= fps:  # 只处理1秒的视频
            print(f"\n🔄 已处理 {frame_count} 帧，停止处理")
            break
    
    # 释放资源
    cap.release()
    out.release()
    
    print(f"\n✅ 视频处理完成!")
    print(f"   总帧数: {frame_count}")
    print(f"   成功处理: {processed_count}")
    print(f"   输出文件: {output_video_path}")
    
    # 验证输出视频
    verify_output_video(output_video_path)
    
    return True

def verify_output_video(video_path):
    """验证输出视频是否可以正常播放"""
    print(f"\n🔍 验证输出视频: {video_path}")
    
    if not os.path.exists(video_path):
        print("❌ 输出视频文件不存在")
        return False
    
    # 检查文件大小
    file_size = os.path.getsize(video_path)
    print(f"   文件大小: {file_size / (1024*1024):.2f} MB")
    
    if file_size == 0:
        print("❌ 输出视频文件为空")
        return False
    
    # 尝试打开视频
    test_cap = cv2.VideoCapture(video_path)
    if not test_cap.isOpened():
        print("❌ 无法打开输出视频文件")
        return False
    
    # 读取第一帧
    ret, frame = test_cap.read()
    if not ret:
        print("❌ 无法读取视频帧")
        test_cap.release()
        return False
    
    # 获取视频信息
    width = int(test_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(test_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = int(test_cap.get(cv2.CAP_PROP_FPS))
    frame_count = int(test_cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"✅ 视频验证成功:")
    print(f"   分辨率: {width} x {height}")
    print(f"   帧率: {fps} FPS")
    print(f"   帧数: {frame_count}")
    
    test_cap.release()
    return True

def create_test_video():
    """创建一个简单的测试视频"""
    print("🎬 创建测试视频...")
    
    # 视频参数
    width, height = 640, 480
    fps = 30
    duration = 3  # 3秒
    total_frames = fps * duration
    
    # 创建视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter('test_video.mp4', fourcc, fps, (width, height), isColor=True)
    
    if not out.isOpened():
        print("❌ 无法创建测试视频")
        return False
    
    # 生成帧
    for i in range(total_frames):
        # 创建彩色帧
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 添加移动的矩形
        x = int((i / total_frames) * (width - 100))
        y = height // 2 - 50
        cv2.rectangle(frame, (x, y), (x + 100, y + 100), (0, 255, 0), -1)
        
        # 添加文字
        text = f"Frame {i+1}/{total_frames}"
        cv2.putText(frame, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        out.write(frame)
    
    out.release()
    print("✅ 测试视频创建完成: test_video.mp4")
    return True

if __name__ == "__main__":
    print("🎥 视频处理脚本")
    print("=" * 50)
    
    # 检查输入视频是否存在
    if not os.path.exists("video.mp4"):
        print("⚠️  输入视频 'video.mp4' 不存在")
        print("🎬 创建测试视频...")
        create_test_video()
        print("💡 请将 'test_video.mp4' 重命名为 'video.mp4' 后重新运行")
    else:
        # 处理视频
        success = process_video_with_api()
        
        if success:
            print("\n🎉 视频处理成功完成!")
        else:
            print("\n❌ 视频处理失败")
