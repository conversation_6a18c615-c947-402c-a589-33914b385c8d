# PLC通信实现说明

## 概述

已在 `orangepi_server.py` 中实现了完整的PLC通信功能，支持自动模式的状态机逻辑。系统通过2000端口监听PLC连接，实现48字节命令协议的双向通信。

## 实现的功能

### 1. 自动模式状态机

实现了完整的自动模式工作流程：

1. **启动自动模式**：PLC第26位=255时启动自动模式
2. **准备阶段**：香橙派返回28位=9（正在准备）
3. **就绪阶段**：准备完成后返回29位=10（准备就绪）
4. **PLC准备入夹**：PLC收到后将29位设为255（PLC准备入夹）
5. **确认入夹**：香橙派返回30位=20（确认开始入夹）
6. **入夹过程**：
   - PLC入夹过程中30位=255（还没到位）
   - PLC到位后31位=255（已到位）
7. **判定阶段**：
   - PLC 32位=255（开始判定）
   - 香橙派返回31位=30（正在判定）
   - 香橙派完成判定后返回32位=40（判定完成）
   - 同时返回40位或41位（NG/OK结果）
8. **结果确认**：PLC收到后36位=255（结果已收到）
9. **循环**：重复准备到判定的整个流程

### 2. 手动模式支持

保留了基本的手动模式框架，可根据需要进一步扩展。

### 3. 心跳机制

第25位作为心跳值，用于实时检测设备运行状态。

## 代码结构

### 主要函数

1. **`plc_socket_listener()`**：监听2000端口的PLC连接
2. **`handle_plc_connection()`**：处理PLC连接的主函数
3. **`parse_plc_data()`**：解析PLC发来的48字节数据
4. **`pack_data_to_plc()`**：打包发送给PLC的48字节数据
5. **`handle_auto_mode_state_machine()`**：自动模式状态机逻辑
6. **`handle_manual_mode_state_machine()`**：手动模式状态机逻辑
7. **`perform_vision_judgment()`**：视觉判定函数（可集成实际视觉系统）

### 全局状态变量

- `current_mode`：当前模式（"manual"/"auto"/"unknown"）
- `auto_state`：自动模式状态
- `manual_state`：手动模式状态
- `heartbeat_counter`：心跳计数器
- `last_judgment_result`：最后判定结果

## 48字节协议定义

### 数据结构
- **前24字节**：6个float32坐标 [x, y, z, rx, ry, rz]
- **第25-42字节**：控制位
- **第43-48字节**：预留

### 关键控制位
- **25位**：心跳值
- **26位**：手动模式标志（255=手动）
- **27位**：自动模式标志（255=自动）
- **28位**：准备状态（9=准备中）
- **29位**：就绪状态（10=就绪，255=PLC准备入夹）
- **30位**：入夹确认（20=确认，255=未到位）
- **31位**：到位信号（255=到位，30=判定中）
- **32位**：判定控制（255=开始判定，40=判定完成）
- **36位**：结果确认（255=已收到）
- **40位**：NG结果（255=NG）
- **41位**：OK结果（255=OK）

## 使用方法

### 1. 启动服务器

运行 `orangepi_server.py`，服务器会自动在2000端口监听PLC连接。

### 2. 测试通信

使用提供的 `test_plc_communication.py` 脚本测试自动模式流程：

```bash
python test_plc_communication.py
```

### 3. 集成视觉系统

在 `perform_vision_judgment()` 函数中集成实际的视觉识别逻辑：

```python
def perform_vision_judgment(localizer) -> str:
    try:
        # 调用实际的视觉定位和判定逻辑
        result = localizer.main_rectify_poses(save=True)
        # 根据result判断OK/NG
        return "OK" if result[0] == 1 else "NG"
    except Exception as e:
        logging.error(f"视觉判定异常: {e}")
        return "NG"
```

## 状态机流程图

```
自动模式状态转换：
idle -> preparing -> ready -> plc_ready -> waiting_position -> waiting_judgment -> judging -> result_sent -> preparing (循环)
```

## 注意事项

1. **线程安全**：使用 `plc_state_lock` 确保状态变量的线程安全访问
2. **异常处理**：所有网络通信和状态转换都有完整的异常处理
3. **日志记录**：详细的日志记录便于调试和监控
4. **模块化设计**：各个功能模块独立，便于维护和扩展

## 扩展建议

1. **视觉系统集成**：将实际的视觉识别系统集成到 `perform_vision_judgment()` 函数
2. **配置文件**：将状态码和超时时间等参数移到配置文件
3. **数据库记录**：记录每次判定的结果和统计信息
4. **Web监控界面**：添加Web界面监控PLC通信状态
5. **故障恢复**：添加通信中断后的自动重连机制
