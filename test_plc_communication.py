#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PLC通信测试脚本
用于测试自动模式的状态机逻辑
"""

import socket
import struct
import time
import threading

def pack_plc_command(pos, heartbeat, manual_flag, auto_flag, **kwargs):
    """
    打包PLC命令数据
    """
    pos_bytes = struct.pack("!6f", *pos)
    
    control_list = [
        heartbeat,                                    # 25 心跳
        manual_flag,                                  # 26 手动
        auto_flag,                                    # 27 自动
        kwargs.get("preparing_request", 0),           # 28 准备请求
        255 if kwargs.get("plc_ready_clamp", False) else 0,  # 29 PLC准备入夹
        255 if kwargs.get("not_in_position", False) else 0,  # 30 未到位
        255 if kwargs.get("in_position", False) else 0,      # 31 到位
        255 if kwargs.get("start_judgment", False) else 0,   # 32 开始判定
        0,  # 33
        0,  # 34
        0,  # 35
        255 if kwargs.get("result_received", False) else 0,  # 36 结果确认
        0,  # 37
        0,  # 38
        0,  # 39
        0,  # 40
        0,  # 41
        0   # 42
    ]
    
    # 填充为48字节
    full_bytes = pos_bytes + bytes(control_list) + bytes(48 - 24 - len(control_list))
    return full_bytes

def parse_orangepi_response(data):
    """
    解析香橙派响应数据
    """
    if len(data) != 48:
        raise ValueError("数据长度错误")
    
    pos_floats = struct.unpack("!6f", data[:24])
    controls = struct.unpack("!18B", data[24:42])
    
    return {
        "position": pos_floats,
        "heartbeat": controls[0],
        "manual": controls[1] == 255,
        "auto": controls[2] == 255,
        "preparing": controls[3],      # 28位状态码
        "ready": controls[4],          # 29位状态码
        "clamp_ack": controls[5],      # 30位状态码
        "judging": controls[6],        # 31位状态码
        "judgment_complete": controls[7],  # 32位状态码
        "result_ng": controls[15] == 255,  # 40位
        "result_ok": controls[16] == 255   # 41位
    }

def test_auto_mode_flow():
    """
    测试自动模式完整流程
    """
    print("开始测试自动模式流程...")
    
    try:
        # 连接到香橙派服务器的2000端口
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect(('localhost', 2000))  # 根据实际IP调整
        print("已连接到香橙派服务器")
        
        heartbeat = 0
        pos = [100.0, 200.0, 300.0, 0.0, 0.0, 0.0]  # 测试坐标
        
        # 步骤1: 发送自动模式信号
        print("\n=== 步骤1: 启动自动模式 ===")
        cmd = pack_plc_command(pos, heartbeat, 0, 255)  # 26位=0, 27位=255
        sock.sendall(cmd)
        response = sock.recv(48)
        parsed = parse_orangepi_response(response)
        print(f"香橙派响应: {parsed}")
        
        time.sleep(1)
        heartbeat += 1
        
        # 步骤2: 等待准备完成
        print("\n=== 步骤2: 等待准备完成 ===")
        for i in range(3):
            cmd = pack_plc_command(pos, heartbeat, 0, 255)
            sock.sendall(cmd)
            response = sock.recv(48)
            parsed = parse_orangepi_response(response)
            print(f"香橙派响应: 准备状态={parsed['preparing']}, 就绪状态={parsed['ready']}")
            
            if parsed['ready'] == 10:
                print("香橙派已准备就绪!")
                break
            
            time.sleep(0.5)
            heartbeat += 1
        
        # 步骤3: PLC准备入夹
        print("\n=== 步骤3: PLC准备入夹 ===")
        cmd = pack_plc_command(pos, heartbeat, 0, 255, plc_ready_clamp=True)
        sock.sendall(cmd)
        response = sock.recv(48)
        parsed = parse_orangepi_response(response)
        print(f"香橙派响应: 入夹确认={parsed['clamp_ack']}")
        
        time.sleep(1)
        heartbeat += 1
        
        # 步骤4: 模拟未到位->到位
        print("\n=== 步骤4: 模拟入夹过程 ===")
        # 未到位
        cmd = pack_plc_command(pos, heartbeat, 0, 255, not_in_position=True)
        sock.sendall(cmd)
        response = sock.recv(48)
        parsed = parse_orangepi_response(response)
        print(f"未到位时香橙派响应: {parsed['clamp_ack']}")
        
        time.sleep(1)
        heartbeat += 1
        
        # 到位
        cmd = pack_plc_command(pos, heartbeat, 0, 255, in_position=True)
        sock.sendall(cmd)
        response = sock.recv(48)
        parsed = parse_orangepi_response(response)
        print(f"到位时香橙派响应: {parsed['clamp_ack']}")
        
        time.sleep(1)
        heartbeat += 1
        
        # 步骤5: 开始判定
        print("\n=== 步骤5: 开始判定 ===")
        cmd = pack_plc_command(pos, heartbeat, 0, 255, start_judgment=True)
        sock.sendall(cmd)
        response = sock.recv(48)
        parsed = parse_orangepi_response(response)
        print(f"判定开始响应: 判定状态={parsed['judging']}")
        
        time.sleep(2)  # 等待判定完成
        heartbeat += 1
        
        # 步骤6: 获取判定结果
        print("\n=== 步骤6: 获取判定结果 ===")
        cmd = pack_plc_command(pos, heartbeat, 0, 255)
        sock.sendall(cmd)
        response = sock.recv(48)
        parsed = parse_orangepi_response(response)
        print(f"判定结果: 完成状态={parsed['judgment_complete']}, OK={parsed['result_ok']}, NG={parsed['result_ng']}")
        
        time.sleep(1)
        heartbeat += 1
        
        # 步骤7: 确认收到结果
        print("\n=== 步骤7: 确认收到结果 ===")
        cmd = pack_plc_command(pos, heartbeat, 0, 255, result_received=True)
        sock.sendall(cmd)
        response = sock.recv(48)
        parsed = parse_orangepi_response(response)
        print(f"结果确认后响应: {parsed}")
        
        print("\n=== 自动模式流程测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
    finally:
        sock.close()

if __name__ == "__main__":
    test_auto_mode_flow()
