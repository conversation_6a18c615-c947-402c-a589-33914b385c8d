import socket
import cv2
import json
import struct
import threading
import time
from datetime import datetime
from server_utils.vision_localizer import VisionLocalizer
import os
from server_utils.logger_config import logger as logging
                    
                    
# 读取配置文件
def read_server_config(config_file):
    logging.info(f"Reading server config from:\n  {config_file}")
    with open(config_file, "r") as f:
        config = json.load(f)

    ip = config["server_config"]["ip"]
    port = config["server_config"]["port"]
    camera_config_path = os.path.join(os.path.dirname(__file__),config["localizer_config"]["camera_config_path"])
    tube_config_path = os.path.join(os.path.dirname(__file__),config["localizer_config"]["tube_configs"]["6"]["tube_config_path"])

    logging.debug(f"Server config details:\n  IP:{ip}\n  port:{port}\n  camera_config_path:{camera_config_path}\n  tube_config_path{tube_config_path}")


    return ip, port, camera_config_path, tube_config_path


def run_localizer_main(image_root,result_root):
    logging.info(f"Starting program...")

    config_file_path = os.path.join(os.path.dirname(__file__), "configs/config.json")
    ip, port, camera_config_path, tube_config_path = read_server_config(config_file_path)

    # 初始化
    logging.info(f"Initializing localizer...")
    picture_save_path = os.path.join(os.path.dirname(__file__), "saved_pictures")
    localizer = VisionLocalizer(camera_config_path, tube_config_path, picture_save_path)
    logging.info(f"Localizer ready.")
    
    #rectify_result = localizer.main_rectify_poses()
    
    for image_name in os.listdir(image_root):
        if 'old' in image_name:
            continue
        print('image_name',image_name)
        image_folder = os.path.join(image_root, image_name)
        if not os.path.isdir(image_folder):
            continue
        image_out_folder = os.path.join(result_root, image_name)
        image_left_path = os.path.join(image_folder, f'{image_name}_LRect.png')
        image_right_path = os.path.join(image_folder, f'{image_name}_RRect.png')
        rectify_result = localizer.main_rectify_poses(mode='test', left_path=image_left_path, right_path=image_right_path, test_save_folder=image_out_folder,image_name=image_name)
        print('rectify_result:',rectify_result)
        

    

if __name__ == "__main__":
    image_root = 'test_img'
    image_root = os.path.join(os.path.dirname(__file__), image_root)
    result_root = 'test_img_result'
    result_root = os.path.join(os.path.dirname(__file__), result_root)
    run_localizer_main(image_root,result_root)
