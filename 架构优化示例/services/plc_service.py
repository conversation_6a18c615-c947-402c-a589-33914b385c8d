#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PLC通信服务
负责处理PLC连接和协议解析
"""

import socket
import threading
import logging
from typing import Optional, Callable

from ..core.state_machine import state_machine, OperationMode, AutoState, ManualState
from ..protocols.plc_protocol import PLCProtocol
from ..core.data_models import P<PERSON>Command, PLCResponse

class PLCService:
    """PLC通信服务"""
    
    def __init__(self, host: str = "0.0.0.0", port: int = 2000):
        self.host = host
        self.port = port
        self.logger = logging.getLogger(__name__)
        self.protocol = PLCProtocol()
        
        self._server_socket: Optional[socket.socket] = None
        self._running = False
        self._server_thread: Optional[threading.Thread] = None
        self._connections = []
        
        # 注册状态变化回调
        state_machine.register_callback('mode_changed', self._on_mode_changed)
        
        # 业务逻辑处理器
        self._auto_handler = AutoModeHandler()
        self._manual_handler = ManualModeHandler()
    
    def start(self):
        """启动PLC服务"""
        if self._running:
            self.logger.warning("PLC服务已在运行")
            return
        
        try:
            self._server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self._server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self._server_socket.bind((self.host, self.port))
            self._server_socket.listen(5)
            
            self._running = True
            self._server_thread = threading.Thread(target=self._server_loop, daemon=True)
            self._server_thread.start()
            
            self.logger.info(f"PLC服务已启动，监听 {self.host}:{self.port}")
            
        except Exception as e:
            self.logger.error(f"启动PLC服务失败: {e}")
            self.stop()
    
    def stop(self):
        """停止PLC服务"""
        self._running = False
        
        # 关闭所有连接
        for conn in self._connections[:]:
            try:
                conn.close()
            except:
                pass
        self._connections.clear()
        
        # 关闭服务器socket
        if self._server_socket:
            try:
                self._server_socket.close()
            except:
                pass
            self._server_socket = None
        
        # 等待服务器线程结束
        if self._server_thread and self._server_thread.is_alive():
            self._server_thread.join(timeout=1.0)
        
        self.logger.info("PLC服务已停止")
    
    def _server_loop(self):
        """服务器主循环"""
        while self._running:
            try:
                conn, addr = self._server_socket.accept()
                self.logger.info(f"PLC连接来自: {addr}")
                
                self._connections.append(conn)
                client_thread = threading.Thread(
                    target=self._handle_client,
                    args=(conn, addr),
                    daemon=True
                )
                client_thread.start()
                
            except Exception as e:
                if self._running:
                    self.logger.error(f"接受PLC连接失败: {e}")
    
    def _handle_client(self, conn: socket.socket, addr):
        """处理PLC客户端连接"""
        try:
            while self._running:
                # 接收PLC数据
                data = self._recv_exact(conn, 48)
                if not data:
                    break
                
                # 解析PLC命令
                command = self.protocol.parse_command(data)
                self.logger.debug(f"收到PLC命令: {command}")
                
                # 处理命令并生成响应
                response = self._process_command(command)
                
                # 发送响应
                response_data = self.protocol.pack_response(response)
                conn.sendall(response_data)
                
        except Exception as e:
            self.logger.error(f"处理PLC连接异常: {e}")
        finally:
            try:
                conn.close()
                if conn in self._connections:
                    self._connections.remove(conn)
            except:
                pass
            self.logger.info(f"PLC连接已断开: {addr}")
    
    def _recv_exact(self, sock: socket.socket, size: int) -> bytes:
        """精确接收指定字节数"""
        data = b''
        while len(data) < size:
            chunk = sock.recv(size - len(data))
            if not chunk:
                raise ConnectionError("连接已断开")
            data += chunk
        return data
    
    def _process_command(self, command: PLCCommand) -> PLCResponse:
        """处理PLC命令"""
        # 更新状态机
        self._update_state_from_command(command)
        
        # 根据模式处理
        current_state = state_machine.get_state()
        
        if current_state.mode == OperationMode.AUTO:
            return self._auto_handler.handle(command, current_state)
        elif current_state.mode == OperationMode.MANUAL:
            return self._manual_handler.handle(command, current_state)
        else:
            return self._create_default_response()
    
    def _update_state_from_command(self, command: PLCCommand):
        """根据PLC命令更新状态机"""
        # 更新心跳
        state_machine.increment_heartbeat()
        
        # 更新位置
        state_machine.set_position(command.position)
        
        # 确定模式
        if command.manual_flag == 255 and command.auto_flag == 255:
            state_machine.set_mode(OperationMode.MANUAL)  # 同时为255优先手动
        elif command.manual_flag == 255:
            state_machine.set_mode(OperationMode.MANUAL)
        elif command.auto_flag == 255:
            state_machine.set_mode(OperationMode.AUTO)
        else:
            state_machine.set_mode(OperationMode.UNKNOWN)
    
    def _create_default_response(self) -> PLCResponse:
        """创建默认响应"""
        current_state = state_machine.get_state()
        return PLCResponse(
            position=current_state.position,
            heartbeat=current_state.heartbeat,
            manual=False,
            auto=False
        )
    
    def _on_mode_changed(self, old_mode, new_mode):
        """模式变化回调"""
        self.logger.info(f"PLC模式变化: {old_mode.value} -> {new_mode.value}")

class AutoModeHandler:
    """自动模式处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.AutoModeHandler")
    
    def handle(self, command: PLCCommand, current_state) -> PLCResponse:
        """处理自动模式命令"""
        # 根据当前状态和命令决定下一步动作
        if current_state.auto_state == AutoState.IDLE:
            state_machine.set_auto_state(AutoState.PREPARING)
            return self._create_response(current_state, preparing=9)
        
        elif current_state.auto_state == AutoState.PREPARING:
            state_machine.set_auto_state(AutoState.READY)
            return self._create_response(current_state, ready=10)
        
        elif current_state.auto_state == AutoState.READY:
            if command.flags.get("plc_ready_clamp"):
                state_machine.set_auto_state(AutoState.PLC_READY)
                return self._create_response(current_state, clamp_ack=20)
            return self._create_response(current_state, ready=10)
        
        # ... 其他状态处理逻辑
        
        return self._create_response(current_state)
    
    def _create_response(self, current_state, **kwargs) -> PLCResponse:
        """创建自动模式响应"""
        return PLCResponse(
            position=current_state.position,
            heartbeat=current_state.heartbeat,
            auto=True,
            **kwargs
        )

class ManualModeHandler:
    """手动模式处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.ManualModeHandler")
    
    def handle(self, command: PLCCommand, current_state) -> PLCResponse:
        """处理手动模式命令"""
        # 手动模式处理逻辑
        return PLCResponse(
            position=current_state.position,
            heartbeat=current_state.heartbeat,
            manual=True
        )
