#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
状态机管理器
集中管理PLC通信的所有状态
"""

import threading
import logging
from enum import Enum
from typing import Optional, Dict, Any
from dataclasses import dataclass

class OperationMode(Enum):
    UNKNOWN = "unknown"
    MANUAL = "manual"
    AUTO = "auto"

class AutoState(Enum):
    IDLE = "idle"
    PREPARING = "preparing"
    READY = "ready"
    PLC_READY = "plc_ready"
    WAITING_POSITION = "waiting_position"
    WAITING_JUDGMENT = "waiting_judgment"
    JUDGING = "judging"
    RESULT_SENT = "result_sent"

class ManualState(Enum):
    IDLE = "idle"
    PREPARING = "preparing"
    READY = "ready"
    PLC_READY = "plc_ready"
    CLAMPING = "clamping"

@dataclass
class PLCState:
    """PLC状态数据类"""
    mode: OperationMode = OperationMode.UNKNOWN
    auto_state: AutoState = AutoState.IDLE
    manual_state: ManualState = ManualState.IDLE
    heartbeat: int = 0
    last_judgment_result: Optional[str] = None
    position: tuple = (0.0, 0.0, 0.0, 0.0, 0.0, 0.0)

class StateMachine:
    """状态机管理器"""
    
    def __init__(self):
        self._lock = threading.RLock()
        self._state = PLCState()
        self._callbacks = {}
        self.logger = logging.getLogger(__name__)
    
    def get_state(self) -> PLCState:
        """获取当前状态（线程安全）"""
        with self._lock:
            return PLCState(
                mode=self._state.mode,
                auto_state=self._state.auto_state,
                manual_state=self._state.manual_state,
                heartbeat=self._state.heartbeat,
                last_judgment_result=self._state.last_judgment_result,
                position=self._state.position
            )
    
    def set_mode(self, mode: OperationMode):
        """设置操作模式"""
        with self._lock:
            old_mode = self._state.mode
            self._state.mode = mode
            
            # 重置状态
            if mode == OperationMode.AUTO:
                self._state.auto_state = AutoState.IDLE
            elif mode == OperationMode.MANUAL:
                self._state.manual_state = ManualState.IDLE
            
            self.logger.info(f"模式切换: {old_mode.value} -> {mode.value}")
            self._notify_callbacks('mode_changed', old_mode, mode)
    
    def set_auto_state(self, state: AutoState):
        """设置自动模式状态"""
        with self._lock:
            if self._state.mode != OperationMode.AUTO:
                self.logger.warning(f"尝试在非自动模式下设置自动状态: {state}")
                return
            
            old_state = self._state.auto_state
            self._state.auto_state = state
            self.logger.info(f"自动状态切换: {old_state.value} -> {state.value}")
            self._notify_callbacks('auto_state_changed', old_state, state)
    
    def set_manual_state(self, state: ManualState):
        """设置手动模式状态"""
        with self._lock:
            if self._state.mode != OperationMode.MANUAL:
                self.logger.warning(f"尝试在非手动模式下设置手动状态: {state}")
                return
            
            old_state = self._state.manual_state
            self._state.manual_state = state
            self.logger.info(f"手动状态切换: {old_state.value} -> {state.value}")
            self._notify_callbacks('manual_state_changed', old_state, state)
    
    def increment_heartbeat(self):
        """递增心跳计数"""
        with self._lock:
            self._state.heartbeat = (self._state.heartbeat + 1) % 256
    
    def set_judgment_result(self, result: str):
        """设置判定结果"""
        with self._lock:
            self._state.last_judgment_result = result
            self.logger.info(f"判定结果: {result}")
            self._notify_callbacks('judgment_result', result)
    
    def set_position(self, position: tuple):
        """设置位置信息"""
        with self._lock:
            self._state.position = position
    
    def register_callback(self, event: str, callback):
        """注册状态变化回调"""
        if event not in self._callbacks:
            self._callbacks[event] = []
        self._callbacks[event].append(callback)
    
    def _notify_callbacks(self, event: str, *args):
        """通知回调函数"""
        if event in self._callbacks:
            for callback in self._callbacks[event]:
                try:
                    callback(*args)
                except Exception as e:
                    self.logger.error(f"回调函数执行失败: {e}")
    
    def reset(self):
        """重置状态机"""
        with self._lock:
            self._state = PLCState()
            self.logger.info("状态机已重置")
            self._notify_callbacks('reset')

# 全局状态机实例
state_machine = StateMachine()
