import ast
import errno
from typing import Any

import numpy as np
import select
import shutil
import socket
import pickle
import json
import struct
import queue
import threading
import logging as std_logging
from threading import Event
import time
import os
import io
import typing as t
from functools import partial
from func.tube import TubeCalibrator
import cv2
from func.camera import calibrate_camera
from client import send_command
from server_utils.vision_localizer import VisionLocalizer
from server_utils.logger_config import logger as logging
from func.chessboard import ChessboardCaptureThread
from scipy.spatial.transform import Rotation

camera_capture = None
aging_active = True
aging_lock = threading.Lock()
t0_stop_event = threading.Event()
t0_thread = None
q0_stop_event = threading.Event()
q0_thread = None
s_stop_event = threading.Event()
s_thread = None
q0_capture_thread = None
q0_encode_thread = None
q0_send_thread = None
calibrator = None
camera_capture = None

with open("configs/config.json", "r", encoding="utf-8") as f:
    config = json.load(f)

camera_params_path = config['localizer_config'].get('camera_config_path',
                                                    r'/home/<USER>/Desktop/orangepi_server/configs/camera_params.pkl')
model_path = config['localizer_config'].get('model_path',
                                            r'/home/<USER>/Desktop/orangepi_server/models/yolov8_obb_2.1.0.rknn')
config_path_1 = config['localizer_config'].get('config_path_1', r'config.json')
out_folder = config['localizer_config'].get('out_folder', r'new_config')
tube_config_path_1 = config['localizer_config'].get('tube_config_path_1', r'tube_config.pkl')

print("camera_params_path:", camera_params_path)
print("model_path:", model_path)
print("config_path_1:", config_path_1)
print("out_folder:", out_folder)
print("tube_config_path_1:", tube_config_path_1)


def read_server_config(config_file, part_id):
    """
    读取配置文件中的：
        IP/Port
        相机配置路径
        与特定管件 part_id 对应的 .pkl 配置路径
        图片保存时长和路径
    """
    logging.info(f"Reading server config from: {config_file}")
    if not os.path.exists(config_file):
        raise FileNotFoundError(f"Config file does not exist: {config_file}")
    with open(config_file, "r") as f:
        config = json.load(f)
    try:
        ip = config["server_config"]["ip"]
        port = config["server_config"]["port"]
        if not isinstance(ip, str) or not ip:
            logging.error("Invalid IP address in config.")
        if not isinstance(port, int) or port <= 0:
            logging.error("Invalid port in config.")
        camera_config_path = os.path.join(os.path.dirname(__file__), config["localizer_config"]["camera_config_path"])
        if not os.path.exists(camera_config_path):
            logging.error(f"Camera config path does not exist: {camera_config_path}")
        tube_config_path_dict = config["localizer_config"]["tube_configs"]
        tube_config_info = tube_config_path_dict.get(str(part_id))
        if not tube_config_info:
            logging.error(f"Invalid part_id: {part_id} (No config found)")
        tube_config_path = os.path.join(os.path.dirname(__file__), tube_config_info["tube_config_path"])
        if not os.path.exists(tube_config_path):
            logging.error(f"Tube config path does not exist: {tube_config_path}")
        img_save_time = config["save_days"]["days_to_keep"]
        if not isinstance(img_save_time, int) or img_save_time <= 0:
            logging.error(f"Invalid image save time: {img_save_time}, must be a positive integer.")
        img_save_location = os.path.join(os.path.dirname(__file__), config["save_days"]["image_folder_path"])
        if not os.path.exists(img_save_location):
            logging.warning(f"Image save location does not exist, creating: {img_save_location}")
            os.makedirs(img_save_location, exist_ok=True)
        logging.info(
            f"Server config details: IP:{ip}, Port:{port}, Camera Config:{camera_config_path}, Tube Config:{tube_config_path}, Save Days:{img_save_time}, Save Path:{img_save_location}")
        return ip, port, camera_config_path, tube_config_path, img_save_time, img_save_location
    except KeyError as e:
        raise KeyError(f"Missing key in config file: {e}")


def handle_command_camera(command, localizer):
    """检测相机是否连接成功，返回状态（0/1）"""
    try:
        logging.info("Checking camera status...")
        cap_success = localizer.camera_is_connected()
        logging.debug(f"Camera status: {cap_success}")
        return struct.pack("!i", cap_success)
    except Exception as e:
        logging.error(f"Error in handle_command_camera: {e}")
        return struct.pack("!i", -1)


def handle_command_localizer(command, localizer, save=False):
    """调用视觉定位的主函数，执行定位。返回包含 1 个状态码 + 6 个 float 位姿数据的结构体"""
    try:
        logging.info(f"Localizing with save={save}...")
        result = localizer.main_rectify_poses(save=save)
        logging.debug(f"Localizer result: {result}")
        return struct.pack("!i6f", *result)
    except Exception as e:
        logging.error(f"Error in handle_command_localizer: {e}")
        default_result = [-1, 9999.999, 9999.999, 9999.999, 9999.999, 9999.999, 9999.999]
        return struct.pack("!i6f", *default_result)


def display_basic(imgL, imgR, client_socket):
    send_command(imgL, imgR, client_socket)


def send_command(imgL, imgR, client_socket):
    _send_with_len(client_socket, imgL)
    _send_with_len(client_socket, imgR)

def capture_thread_func(cap, frame_queue, stop_event):
    import cv2, time, logging
    while not stop_event.is_set():
        try:
            t_start = time.time()
            frames = cap.capture()
            t_capture = time.time()

            if frames and frames[0] is not None and frames[1] is not None:
                frameL = cv2.resize(frames[0], (320, 240))
                frameR = cv2.resize(frames[1], (320, 240))
                combined_img = cv2.hconcat([frameL, frameR])
                try:
                    frame_queue.put_nowait(combined_img)
                except queue.Full:
                    try:
                        _ = frame_queue.get_nowait()
                        frame_queue.put_nowait(combined_img)
                    except queue.Empty:
                        pass

            t_end = time.time()
            # logging.info(f"采集耗时: {(t_capture - t_start)*1000:.1f}ms, 队列存放耗时: {(t_end - t_capture)*1000:.1f}ms")
            time.sleep(0.02)
        except Exception as e:
            logging.error(f"采集线程异常: {e}")


def encode_thread_func(frame_queue, send_queue, stop_event):
    import cv2, time, logging
    while not stop_event.is_set():
        try:
            try:
                frame = frame_queue.get(timeout=0.1)
            except queue.Empty:
                continue

            t_start = time.time()
            success, img_encoded = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 60])
            t_encode = time.time()
            if not success:
                logging.error("图像编码失败")
                continue

            img_data = img_encoded.tobytes()
            try:
                send_queue.put_nowait(img_data)
            except queue.Full:
                try:
                    _ = send_queue.get_nowait()
                    send_queue.put_nowait(img_data)
                except queue.Empty:
                    pass
            t_end = time.time()
            # logging.info(f"编码耗时: {(t_encode - t_start)*1000:.1f}ms, 队列存放耗时: {(t_end - t_encode)*1000:.1f}ms")
        except Exception as e:
            logging.error(f"编码线程异常: {e}")


def send_thread_func(send_queue, sock, stop_event):
    import struct, time, logging
    while not stop_event.is_set():
        try:
            try:
                img_data = send_queue.get(timeout=0.1)
            except queue.Empty:
                continue

            t_start = time.time()
            size_data = struct.pack("!I", len(img_data))
            try:
                sock.sendall(size_data + img_data)
            except Exception as e:
                logging.error(f"发送失败: {e}")
                stop_event.set()
                break
            t_end = time.time()
            # logging.info(f"发送耗时: {(t_end - t_start)*1000:.1f}ms")
        except Exception as e:
            logging.error(f"发送线程异常: {e}")
            stop_event.set()
            break


def _send_with_len(sock, img_pair):
    frameL, frameR = img_pair
    combined_img = cv2.hconcat([frameL, frameR])
    _, img_encoded = cv2.imencode('.jpg', combined_img)
    img_data = img_encoded.tobytes()
    size_data = struct.pack("!I", len(img_data))
    sock.sendall(size_data)
    sock.sendall(img_data)


def _send_image(sock, image):
    _, img_encoded = cv2.imencode(".jpg", image)
    data = img_encoded.tobytes()
    sock.sendall(struct.pack("!I", len(data)))
    sock.sendall(data)


def handle_command_send_image(localizer):
    try:
        frames = localizer.get_current_frame()  # [frameL, frameR]
        if frames is None or frames[0] is None or frames[1] is None:
            return struct.pack("!i", -1)
        frameL, frameR = frames
        combined_img = cv2.hconcat([frameL, frameR])
        _, img_encoded = cv2.imencode('.jpg', combined_img)
        img_data = img_encoded.tobytes()
        size_data = struct.pack("!I", len(img_data))
        logging.info(f"Sending combined image data of length: {len(img_data)} bytes")
        return size_data + img_data
    except Exception as e:
        logging.error(f"Error in handle_command_send_image: {e}")
        return struct.pack("!i", -1)


def client_handler(client_socket, address, localizer):
    """用于和上位机的命令交互"""
    client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 262144)
    global q0_capture_thread, q0_encode_thread, q0_send_thread, q0_stop_event, calibrator, camera_capture
    global t0_stop_event, t0_thread, aging_active, q0_stop_event, q0_thread, s_stop_event, s_thread
    frame_queue = queue.Queue(maxsize=1)
    send_queue = queue.Queue(maxsize=1)
    if calibrator is None:
        calibrator = TubeCalibrator(camera_params_path, model_path, config_path_1, out_folder, tube_config_path_1,
                                    localizer.cap)

    if camera_capture is None:
        camera_capture = ChessboardCaptureThread(localizer.cap)
    try:
        sock_file = client_socket.makefile('rb')
        while True:
            command = recv_line(client_socket)
            print(f"[Server] 接收到命令: {command}")
            if not command:
                break

            elif command == "status":
                client_socket.sendall(struct.pack("!i", 1))

            elif command == "Q_0":
                """启动实时图像流 Q_0 模式"""
                logging.info("进入 Q_0 实时图像流模式")

                q0_stop_event.set()
                if q0_capture_thread and q0_capture_thread.is_alive():
                    q0_capture_thread.join()
                if q0_encode_thread and q0_encode_thread.is_alive():
                    q0_encode_thread.join()
                if q0_send_thread and q0_send_thread.is_alive():
                    q0_send_thread.join()

                # q0_stop_event = threading.Event()
                q0_stop_event.clear()
                frame_queue = queue.Queue(maxsize=1)
                send_queue = queue.Queue(maxsize=1)

                q0_capture_thread = threading.Thread(
                    target=capture_thread_func,
                    args=(localizer.cap, frame_queue, q0_stop_event),
                    daemon=True
                )
                q0_encode_thread = threading.Thread(
                    target=encode_thread_func,
                    args=(frame_queue, send_queue, q0_stop_event),
                    daemon=True
                )
                q0_send_thread = threading.Thread(
                    target=send_thread_func,
                    args=(send_queue, client_socket, q0_stop_event),
                    daemon=True

                )
                q0_capture_thread.start()
                q0_encode_thread.start()
                q0_send_thread.start()

            elif command == "Q_stop":
                """停止实时显示线程"""
                logging.warning("收到 Q_stop 命令，准备停止线程")
                q0_stop_event.set()
                for t in [q0_capture_thread, q0_encode_thread, q0_send_thread]:
                    if t and t.is_alive():
                        t.join()
                logging.info("Q_0 图像流线程已停止")
                client_socket.sendall(struct.pack("!i", 1))


            elif command == "Q_0_N":
                """棋盘格检测结果跳过"""
                camera_capture.resume()
                client_socket.sendall(struct.pack("!i", 1))
                logging.info("客户端跳过保存，继续采集")

            elif command == "Q_0_S":
                """棋盘格检测结果保存"""
                result = camera_capture.save_current_frame()
                camera_capture.resume()
                client_socket.sendall(struct.pack("!i", result))
                logging.info("已保存图像，发送路径给客户端")

            elif command == "S":
                """开始棋盘格检测"""
                if q0_stop_event:
                    q0_stop_event.set()
                    if q0_capture_thread and q0_capture_thread.is_alive():
                        q0_capture_thread.join()
                    if q0_encode_thread and q0_encode_thread.is_alive():
                        q0_encode_thread.join()
                    if q0_send_thread and q0_send_thread.is_alive():
                        q0_send_thread.join()
                    logging.info("已成功停止 Q_0 图像流线程")
                logging.info("进入 S 图像流模式")
                s_stop_event.clear()

                def _send_frame_async(sock, frame_pair):
                    """异步编码并发送图像数据"""
                    try:
                        combined_img = cv2.hconcat(frame_pair)
                        _, img_encoded = cv2.imencode('.jpg', combined_img)
                        img_data = img_encoded.tobytes()
                        size_data = struct.pack("!I", len(img_data))
                        sock.sendall(size_data + img_data)
                    except Exception as e:
                        logging.error(f"[发送图像失败]: {e}")
                        s_stop_event.set()

                def s_loop():
                    try:
                        # client_socket.settimeout(0.1)
                        camera_capture.start()
                        while not s_stop_event.is_set():
                            if camera_capture.paused_after_detection:
                                frames = camera_capture.get_latest_frames()
                            else:
                                frames = camera_capture.get_latest_raw_frame()
                            if frames:
                                threading.Thread(
                                    target=_send_frame_async,
                                    args=(client_socket, frames),
                                    daemon=True
                                ).start()
                            time.sleep(0.03)
                    except Exception as e:
                        logging.error(f"S 图像流出错: {e}")
                    finally:
                        camera_capture.stop()
                        logging.info("S 图像流线程退出")

                s_thread = threading.Thread(target=s_loop, daemon=True)
                s_thread.start()

            elif command == "S_stop":
                """停止棋盘格检测"""
                if s_thread and s_thread.is_alive():
                    s_stop_event.set()
                    s_thread.join()
                    logging.info("S 图像流线程已停止")
                client_socket.sendall(struct.pack("!i", 1))

            elif command == "C_0":
                """相机标定"""
                logging.warning(f"command: {command}")
                try:
                    root_path = config["calibration_config"]["chessboard_image_root"]
                    camera_config_folder = config["calibration_config"]["camera_config_folder"]
                    os.makedirs(camera_config_folder, exist_ok=True)
                    left_img_path = os.path.join(root_path, "左")
                    right_img_path = os.path.join(root_path, "右")
                    camera_config_path = os.path.join(camera_config_folder, "相机参数.pkl")
                    try:
                        path, retS, T = calibrate_camera(left_img_path, right_img_path, camera_config_path,
                                                         chessboard_size=(8, 6), square_size=25)
                    except Exception as e:
                        logging.warning(f"[ERR] 标定异常: {e}")
                    path_bytes = path.encode("utf-8")
                    T_bytes = struct.pack("!3d", *T.flatten())
                    retS_bytes = struct.pack("!d", retS)
                    response = struct.pack("!I", len(path_bytes)) + path_bytes + retS_bytes + T_bytes
                    client_socket.sendall(response)
                    logging.warning(f"len={len(path_bytes)}, retS_bytes={retS_bytes}, T_bytes={T_bytes}")
                except Exception as e:
                    logging.error(f"相机标定失败: {e}")
                    client_socket.sendall(b"CALIBRATION_FAILED")
                continue


            elif command == "T_0":
                """导管标定"""
                logging.info("进入 T_0 导管标定实时模式")
                t0_stop_event.clear()

                def t0_loop():
                    try:
                        while not t0_stop_event.is_set():
                            result = calibrator.capture_and_process()
                            if result is None:
                                logging.error("capture_and_process() 返回 None，可能是相机未就绪或帧获取失败")
                                break
                            (left_vis, right_vis), point3d = result
                            if left_vis is not None and right_vis is not None:
                                combined_img = cv2.hconcat([left_vis, right_vis])
                                _, img_encoded = cv2.imencode('.jpg', combined_img)
                                img_data = img_encoded.tobytes()
                                if point3d is not None:
                                    print(f"3D点坐标: {point3d}")
                                    point3d_json = json.dumps({'point3d': list(point3d)})
                                else:
                                    point3d_json = json.dumps({'point3d': None})
                                point3d_bytes = point3d_json.encode('utf-8')
                                img_len = struct.pack("!I", len(img_data))
                                point_len = struct.pack("!I", len(point3d_bytes))
                                client_socket.sendall(img_len + img_data + point_len + point3d_bytes)
                            else:
                                logging.warning("未获取到图像数据")
                            time.sleep(0.02)
                    except Exception as e:
                        logging.error(f"T_0 处理异常: {e}")
                    finally:
                        logging.warning("T_0 模式已退出")

                t0_thread = threading.Thread(target=t0_loop, daemon=True)
                t0_thread.start()

            elif command == "S_M_Y":
                """加载视觉模型"""
                model_path_bytes = model_path.encode("utf-8")
                response = struct.pack("!I", len(model_path_bytes))
                response += model_path_bytes
                client_socket.sendall(response)

            elif command == "C_M":
                """加载相机参数"""
                try:
                    model_path_bytes = camera_params_path.encode("utf-8")
                    response = struct.pack("!I", len(model_path_bytes))
                    response += model_path_bytes
                    client_socket.sendall(response)
                except Exception as e:
                    logging.error(f"相机参数加载失败: {e}")
                    client_socket.sendall(struct.pack("!i", 0))

            elif command == "T_0_S":
                """保存导管标定参数，并退出"""
                if t0_thread and t0_thread.is_alive():
                    t0_stop_event.set()
                    t0_thread.join()
                    logging.info("T_0 模式线程已停止")
                    key_folder = r'new_config/tube_config.pkl'
                    out_path = r'configs/path'
                    os.makedirs(out_path, exist_ok=True)
                    dst_path = os.path.join(out_path, 'tube_config.pkl')
                    try:
                        shutil.copy2(key_folder, dst_path)
                        logging.info(f"配置文件已复制到 {dst_path}")
                    except Exception as e:
                        logging.error(f"复制 tube_config.pkl 失败: {e}")
                    result = dst_path
                    model_path_bytes = result.encode("utf-8")
                    response = struct.pack("!I", len(model_path_bytes))
                    response += model_path_bytes
                    client_socket.sendall(response)

            elif command == "Y_VAL":
                """更新Y轴坐标"""
                logging.warning(f"XXXXX[Server] 接收到命令: '{command}'")
                try:
                    # y_bytes = client_socket.recv(4)
                    y_bytes = recv_exact(client_socket, 4)
                    if len(y_bytes) < 4:
                        raise ValueError("Y坐标字节不足")
                    y_value = struct.unpack("!i", y_bytes)[0]
                    logging.info(f"[Server] 接收到 Y 值: {y_value}")
                    result = calibrator.process_y_value(y_value)
                    status_code = 1 if result else 0
                    client_socket.sendall(struct.pack("!i", status_code))
                except Exception as e:
                    logging.error(f"处理 Y_VAL 命令失败: {e}")
                    client_socket.sendall(struct.pack("!i", 0))

            elif command == "P_0":
                """偏移量计算"""
                command_id = "3"
                tube_config_path = "./configs/tube_config_6.pkl"
                try:
                    config_file_path = os.path.join(os.path.dirname(__file__), "configs/config.json")
                    ip, port, camera_config_path, _, img_save_time, img_save_location = read_server_config(
                        config_file_path, part_id="6")
                    localizer.update_tube_config(tube_config_path)
                    logging.info(f"Tube config updated to: {tube_config_path}")
                    response = handle_command_localizer(command_id, localizer, save=True)
                    if isinstance(response, bytes) and len(response) == 28:
                        try:
                            status_code, x, y, z, rx, ry, rz = struct.unpack("!i6f", response)
                            if status_code == 1:
                                delta_pos = [x, y, z, rx, ry, rz]
                                offset_path = os.path.join(os.path.dirname(__file__), "configs/last_offset.json")
                                with open(offset_path, "w") as f:
                                    json.dump({"delta_pos_deg": delta_pos}, f, indent=4)
                                logging.info(f"偏移量已保存至 {offset_path}")
                            else:
                                logging.warning(f"偏移量状态异常: status={status_code}")
                        except Exception as e:
                            logging.error(f"偏移量保存失败: {e}")
                except Exception as e:
                    logging.error(f"Error handling Q_0 command: {e}")
                    response = struct.pack("!i", -1)
                client_socket.sendall(response)
            else:
                logging.warning(f"[OrangePi] 未处理的命令: {command}")
    except Exception as e:
        logging.error(f"Error in client_handler: {e}")
        client_socket.close()
        logging.info(f"Client disconnected: {address}")


def recv_exact(sock, size):
    data = b''
    while len(data) < size:
        part = sock.recv(size - len(data))
        logging.warning(f"[DEBUG] 收到 {len(part)} 字节: {part.hex()}")
        if not part:
            raise ConnectionError("连接关闭或无数据")
        data += part
    return data


def recv_line(sock):
    data = b""
    while not data.endswith(b"\n"):
        part = sock.recv(1)
        logging.warning(f"[DEBUG] 收到 {len(part)} 字节: {part.hex()}")
        if not part:
            break
        data += part
    return data.decode().strip()


def delete_old_images(img_save_time, img_save_location):
    """用于定期删除超出保存天数的旧图片，防止磁盘爆满"""
    current_time = time.time()
    for filename in os.listdir(img_save_location):
        file_path = os.path.join(img_save_location, filename)
        if os.path.isfile(file_path) and filename.lower().endswith((".jpg", ".jpeg", ".png", ".bmp", ".gif")):
            file_mtime = os.path.getmtime(file_path)
            file_age_days = (current_time - file_mtime) / (60 * 60 * 24)
            if file_age_days > img_save_time:
                logging.info(f"Deleting file: {file_path} (Age: {int(file_age_days)} days)")
                os.remove(file_path)


def delete_images_periodically(img_save_time, img_save_location, interval=86400):
    """用于定期删除超出保存天数的旧图片，防止磁盘爆满"""
    delete_old_images(img_save_time, img_save_location)
    threading.Timer(interval, delete_images_periodically, args=(img_save_time, img_save_location, interval)).start()


def pos_to_matrix(pos, **options):
    x, y, z, rx, ry, rz = pos
    mode = options.get("mode", 'ZYX')
    matrix_3d = Rotation.from_euler(mode, [rx, ry, rz]).as_matrix()

    arr_xyz = np.array([x, y, z]).reshape(3, 1)
    matrix_4d = np.hstack((matrix_3d, arr_xyz))
    arr_aux = np.array([0, 0, 0, 1]).reshape(1, 4)
    matrix_4d_2 = np.vstack((matrix_4d, arr_aux))

    return matrix_4d_2


def deg_to_rad(degree):
    PI = 3.1415926
    return degree / 180 * PI


def deg_pos_2_rad_pos(degPos):
    # rx, ry, rz为角度
    x, y, z, rx, ry, rz = degPos
    rx = deg_to_rad(rx)
    ry = deg_to_rad(ry)
    rz = deg_to_rad(rz)
    return [x, y, z, rx, ry, rz]


def matrix_to_pos(matrix4d, **options):
    mode = options.get("mode", 'ZYX')
    x = matrix4d[0, 3]
    y = matrix4d[1, 3]
    z = matrix4d[2, 3]
    matrix3d = matrix4d[0:3, 0:3]

    rpy = Rotation.from_matrix(matrix3d).as_euler(mode)
    rx = rpy[0]
    ry = rpy[1]
    rz = rpy[2]

    res = [x, y, z, rx, ry, rz]
    return res

def pack_data_to_plc(pos: list[float], flags: dict, heartbeat: int = 0) -> bytes:
    """
    将香橙派要发给PLC的状态数据打包成48字节（前24位为坐标，25~42为控制状态位）

    :param pos: 6个float32，例如机械臂反馈位姿 [x, y, z, rx, ry, rz]
    :param flags: 字典形式的控制标志，每项为 True/False
    :param heartbeat: 心跳值（通常每帧+1 或恒定）
    :return: 48字节的bytes数据
    """
    if len(pos) != 6:
        raise ValueError("坐标长度必须为6")

    pos_bytes = struct.pack("!6f", *pos)

    # 按照第25~42位的控制位定义顺序排列，每个标志为0或255
    control_list = [
        heartbeat,  # 25 心跳
        255 if flags.get("manual", False) else 0,  # 26 手动
        255 if flags.get("auto", False) else 0,  # 27 自动
        255 if flags.get("preparing", False) else 0,  # 28 正在准备
        255 if flags.get("ready", False) else 0,  # 29 准备就绪
        255 if flags.get("plc_request_clamp", False) else 0,  # 30 PLC请求入夹
        255 if flags.get("judging", False) else 0,  # 31 正在判定
        255 if flags.get("judged", False) else 0,  # 32 判定完成
        255 if flags.get("clamping", False) else 0,  # 33 正在夹紧
        255 if flags.get("clamped", False) else 0,  # 34 已夹紧
        255 if flags.get("releasing", False) else 0,  # 35 正在松夹
        255 if flags.get("released", False) else 0,  # 36 已松夹
        255 if flags.get("working", False) else 0,  # 37 工作状态
        255 if flags.get("clamp_release_feedback", False) else 0,  # 38 松夹反馈
        255 if flags.get("clamp_grip_feedback", False) else 0,  # 39 夹紧反馈
        255 if flags.get("result_ng", False) else 0,  # 40 判定结果NG
        255 if flags.get("result_ok", False) else 0,  # 41 判定结果OK
        255 if flags.get("clamp_error", False) else 0  # 42 夹爪异常
    ]

    # 填充为 48 字节（前24坐标 + 18控制位 + 6备用）
    full_bytes = pos_bytes + bytes(control_list) + bytes(48 - 24 - len(control_list))
    return full_bytes

def parse_plc_data(data: bytes) -> dict:
    """
    解析 PLC 发来的 48 字节控制数据包
    :param data: 原始字节流
    :return: dict 包含位置、模式、心跳、功能标志等
    """
    if len(data) != 48:
        raise ValueError("数据长度错误，应为48字节")
    pos_floats = struct.unpack("!6f", data[:24])
    controls = struct.unpack("!13B", data[24:37])

    heartbeat = controls[0] #心跳
    manual_flag = controls[1] #手动
    auto_flag = controls[2] # 自动

    flags = {   #映射成bool类型的字典
        "init_request": controls[3] == 255,  # 28位
        "plc_request_clamp": controls[4] == 255,  # 29位
        "not_in_position": controls[5] == 255,  # 30位
        "in_position": controls[6] == 255,  # 31位
        "start_judgment": controls[7] == 255,  # 32位
        "clamp_release": controls[8] == 255,  # 33位
        "clamp_grip": controls[9] == 255,  # 34位
        "judgment_start": controls[10] == 255,  # 35位
        "result_received": controls[11] == 255  # 36位
    }
    mode = "unknown"
    if manual_flag == 255:
        mode = "manual"
        handle_manual_mode(flags)
    elif auto_flag == 255:
        mode = "auto"
        handle_auto_mode(flags)
    else:
        mode = "unknown"

    return {
        "position": pos_floats,  # 六轴坐标
        "heartbeat": heartbeat,  # 心跳字节
        "mode": mode,  # 模式（manual/auto/unknown）
        "flags": flags,  # 所有控制信号布尔字典
        "raw_bytes": data  # 原始数据保留
    }

def handle_manual_mode(flags: dict):
    if flags.get("clamp_grip"):
        logging.info("[Manual] 收到手动夹紧指令")
    if flags.get("clamp_release"):
        logging.info("[Manual] 收到手动松夹指令")

def handle_auto_mode(flags: dict):
    if flags.get("plc_request_clamp"):
        logging.info("[Auto] PLC 请求入夹，准备判断是否允许夹紧")
        if flags.get("in_position"):
            logging.info("[Auto] 机器人已到位，执行夹紧流程")
            # TODO: 执行自动夹紧逻辑
        else:
            logging.warning("[Auto] 机器人未就位，暂不夹紧")

    if flags.get("start_judgment"):
        logging.info("[Auto] PLC 请求开始判定，调用图像识别模块")
        # TODO: 启动视觉判定模块

    if flags.get("judged") and flags.get("result_received"):
        logging.info("[Auto] 判定完成并已收到结果，可进行后续处理")

def _pos_correct(
        sock: socket.socket,
        delta_pos_deg: t.List[float],
        data_bytes: bytes
) -> t.Tuple[int, t.Any]:
    """ control the robot for correcting the pipe-end to welding-center """
    try:
        coords = struct.unpack("!6f", data_bytes[:24])
        robot_to_clip_pos = list(coords)
        controls = struct.unpack("!8B", data_bytes[24:32])
        logging.info(f"[Robot] Received coords: {robot_to_clip_pos}")
        logging.info(f"[Robot] Received control flags: {controls}")
    except Exception as e:
        logging.warning(f"坐标解析异常: {e}")
        return -1, None
    robot_to_clip_matrix = pos_to_matrix(robot_to_clip_pos)

    delta_pos = deg_pos_2_rad_pos(delta_pos_deg)
    delta_pos_matrix = pos_to_matrix(delta_pos)

    weld_to_robot5_pos_deg = [-450, -350, -200, 0, 0, 0]

    weld_to_robot_pos = deg_pos_2_rad_pos(weld_to_robot5_pos_deg)
    weld_to_robot_matrix = pos_to_matrix(weld_to_robot_pos)
    robot_to_weld_matrix = np.linalg.inv(weld_to_robot_matrix)

    pipe_to_clip_matrix = np.dot(np.dot(delta_pos_matrix, weld_to_robot_matrix), robot_to_clip_matrix)

    weld_to_target_matrix = pipe_to_clip_matrix
    robot_to_target_matrix = np.dot(robot_to_weld_matrix, weld_to_target_matrix)
    robot_to_target_pos = matrix_to_pos(robot_to_target_matrix)  # robot_to_target_pos
    # packed = struct.pack("!6d", *robot_to_target_pos)
    robot_to_target_pos_f32 = [float(x) for x in robot_to_target_pos]
    coords_packed = struct.pack("!6f", *robot_to_target_pos_f32)
    controls_out = controls
    controls_packed = struct.pack("!8B", *controls_out)
    reserved_bytes = b'\x00' * 16
    response = coords_packed + controls_packed + reserved_bytes
    sock.sendall(response)
    logging.info(f"发送校正结果和控制字节，共48字节")

    return 0, robot_to_target_pos

def robot_socket_listener(host: str, port: int, localizer):
    """
    用于监听机器人端口（如9999），接收机器人发来的坐标等数据
    """
    robot_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    robot_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    robot_socket.bind((host, port))
    robot_socket.listen(5)
    logging.info(f"[Robot] Listening on {host}:{port}")

    while True:
        conn, addr = robot_socket.accept()
        logging.info(f"[Robot] Connection from {addr}")
        threading.Thread(
            target=handle_robot_connection,
            args=(conn, addr, localizer),
            daemon=True
        ).start()

def load_last_offset():
    try:
        with open("configs/last_offset.json", "r") as f:
            data = json.load(f)
            return data.get("delta_pos_deg", [0.0, 0.0, 0.0, 0.0, 0.0, 0.0])
    except Exception as e:
        logging.warning(f"加载偏移量失败: {e}")
        return [0.0] * 6


def handle_robot_connection(conn: socket.socket, addr, localizer):
    logging.info(f"[Robot] Connected: {addr}")
    try:
        while True:
            data = recv_exact(conn, 48)
            # delta_pos_deg = [1.0, 0.0, 0.0, 0.0, 0.0, 0.0]
            delta_pos_deg = load_last_offset()
            status, result = _pos_correct(conn, delta_pos_deg, data)
            if status != 0:
                logging.warning(f"校正失败，断开连接")
                break
    except ConnectionError:
        logging.warning(f"[Robot] Connection closed by {addr}")
    except Exception as e:
        logging.error(f"[Robot] Exception: {e}")
    finally:
        conn.close()
        logging.info(f"[Robot] Connection closed: {addr}")

def server_main():
    """
    初始化本地视觉定位器 VisionLocalizer
        启动摄像头
        启动 socket 监听客户端连接
        为每个客户端开线程调用 client_handler
        异常退出时释放资源
    """
    logging.info("Starting server...")
    print("Starting server...")
    config_file_path = os.path.join(os.path.dirname(__file__), "configs/config.json")
    part_id = "6"
    try:
        ip, port, camera_config_path, tube_config_path, img_save_time, img_save_location = read_server_config(
            config_file_path, part_id)
    except ValueError as e:
        logging.warning(f"Configuration warning: {e}")
        return
    logging.info("Initializing localizer...")
    print("Starting server...")
    picture_save_path = os.path.join(os.path.dirname(__file__), "saved_pictures")
    logging.info(f"open {picture_save_path}")
    localizer = VisionLocalizer(camera_config_path, tube_config_path, picture_save_path)
    logging.info(f"Server localizer on {localizer}")

    delete_images_periodically(img_save_time, img_save_location)
    # 启动机器人 9999 监听
    robot_listener_thread = threading.Thread(
       target=robot_socket_listener,
       args=(ip, 2000, localizer),
       daemon=True
    )
    robot_listener_thread.start()
    # 启动 socket 8888 监听
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    server_socket.bind((ip, port))
    server_socket.listen(1)
    logging.info(f"Server listening on {ip}:{port}")
    try:
        while True:
            client_socket, address = server_socket.accept()
            client_thread = threading.Thread(target=client_handler, args=(client_socket, address, localizer))
            client_thread.start()
    except KeyboardInterrupt:
        logging.warning("KeyboardInterrupt, shutting down server...")
        server_socket.close()


class TextHandler(std_logging.Handler):
    """用于将日志写入 GUI 的 ScrolledText 文本区域，实现实时日志显示"""

    def __init__(self, text_widget):
        super().__init__()
        self.text_widget = text_widget

    def emit(self, record):
        msg = self.format(record)

        def append():
            self.text_widget.configure(state='normal')
            self.text_widget.insert(tk.END, msg + '\n')
            self.text_widget.configure(state='disabled')
            self.text_widget.yview(tk.END)

        self.text_widget.after(0, append)


shutdown_event = Event()
server_socket_global = None


def start_server():
    """在界面加载后调用，启动服务线程（调用 server_main_wrapper）"""
    global server_socket_global
    shutdown_event.clear()
    server_thread = threading.Thread(target=server_main_wrapper, daemon=True)
    server_thread.start()
    log_text.insert(tk.END, "服务器启动中...\n")


def server_main_wrapper():
    """包装了 server_main，用于异常捕获与线程调用"""
    global server_socket_global
    try:
        server_main()
    except Exception as e:
        logging.error(f"服务器异常: {e}")
    finally:
        server_socket_global = None


def shutdown_server():
    """
    点击“关闭服务”按钮后调用：
        设置关闭标志
        关闭 socket
        关闭主界面
    """
    shutdown_event.set()
    global server_socket_global
    if server_socket_global:
        try:
            server_socket_global.close()
            log_text.insert(tk.END, "正在关闭服务器连接...\n")
        except Exception as e:
            logging.error(f"关闭服务器异常: {e}")
    else:
        log_text.insert(tk.END, "服务器未运行\n")
    root.after(1000, root.destroy)


if __name__ == "__main__":
    import tkinter as tk
    from tkinter.scrolledtext import ScrolledText

    root = tk.Tk()
    root.title("视觉定位服务器控制台")
    root.geometry("1200x800")
    root.configure(bg="#2e2e2e")

    log_text = ScrolledText(root,
                            state='disabled',
                            bg="black",
                            fg="#00FF00",
                            font=("Consolas", 12),
                            padx=10,
                            pady=10)
    log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    control_frame = tk.Frame(root, bg="#2e2e2e")
    control_frame.pack(fill=tk.X, padx=10, pady=5)

    btn_style = {
        "font": ("Arial", 12),
        "width": 15,
        "relief": tk.GROOVE,
        "bd": 2
    }

    shutdown_btn = tk.Button(control_frame,
                             text="关闭服务",
                             command=shutdown_server,
                             bg="#F44336",
                             fg="white",
                             **btn_style)
    shutdown_btn.pack(side=tk.RIGHT, padx=5)

    ui_handler = TextHandler(log_text)
    formatter = std_logging.Formatter("%(asctime)s [%(levelname)s] %(message)s")
    ui_handler.setFormatter(formatter)
    std_logging.getLogger().addHandler(ui_handler)

    root.after(100, start_server)

    root.mainloop()
