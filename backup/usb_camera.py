import logging

import cv2
import threading
import time
from queue import Queue

class USBCamera:

    def __init__(self, max_queue_size=5):
        if self._initialized:
            logging.warning("[USBCamera] 已初始化，跳过")
            return
        logging.warning("[USBCamera] 初始化开始")
        # self._initialized = True

        self.connect_camera()
        logging.warning("[USBCamera] 已连接摄像头")
        if not self.cap.isOpened():
            logging.error("[USBCamera] 摄像头打开失败！请检查设备号和连接")
            return

        self.camera_is_configured = False

        if self.camera_is_connected() == 0:
            self.configure_camera()
            logging.warning("[USBCamera] 摄像头已配置")

        self.running = True
        self.frame_queue = Queue(maxsize=max_queue_size)
        self.thread = threading.Thread(target=self._update_frame, daemon=True)
        self.thread.start()
        logging.warning("[USBCamera] 摄像头线程已启动")

    def connect_camera(self):
        self.cap = cv2.VideoCapture(0)

    def release_camera(self):
        self.running = False
        self.thread.join()
        self.cap.release()

    def configure_camera(self):
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        self.camera_is_configured = True

    def camera_is_connected(self):
        return 0 if self.cap and self.cap.isOpened() else -1

    def _update_frame(self):
        logging.warning("摄像头线程已启动")
        while self.running:
            ret, frame = self.cap.read()
            if ret:
                if self.frame_queue.full():
                    _ = self.frame_queue.get()
                self.frame_queue.put(frame)
            else:
                logging.warning("摄像头读取失败")
            time.sleep(0.01)

    def capture(self):
        if self.camera_is_connected() == -1:
            return None

        try:
            frame = self.frame_queue.get(timeout=1)  # 等待1秒内新帧
        except:
            return None

        frameL = frame[0:480, 0:640]
        frameR = frame[0:480, 640:1280]
        return [frameL, frameR]
