#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的PLC通信测试
"""

import socket
import struct
import time

def test_plc_connection():
    """测试PLC连接"""
    print("开始测试PLC连接...")
    
    # 构造测试数据
    pos = [100.0, 200.0, 300.0, 0.0, 0.0, 0.0]  # 6个float32坐标
    pos_bytes = struct.pack("!6f", *pos)
    
    # 控制字节：设置自动模式 (27位=255)
    ctrl_bytes = bytearray(24)  # 24字节控制位
    ctrl_bytes[0] = 1    # 25位心跳
    ctrl_bytes[1] = 0    # 26位手动模式
    ctrl_bytes[2] = 255  # 27位自动模式
    ctrl_bytes[3] = 255
    ctrl_bytes[4] = 255
    
    data = pos_bytes + bytes(ctrl_bytes)
    
    print(f"数据长度: {len(data)} 字节")
    print(f"数据内容: {data.hex()}")
    
    # 尝试连接不同的IP
    test_ips = ["localhost", "127.0.0.1", "*************"]
    
    for ip in test_ips:
        print(f"\n尝试连接 {ip}:2000...")
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3.0)
            sock.connect((ip, 2000))
            
            print(f"✅ 连接成功: {ip}:2000")
            
            # 发送数据
            print("发送数据...")
            sock.sendall(data)
            print("✅ 数据发送成功")
            
            # 接收响应
            print("等待响应...")
            response = sock.recv(48)
            print(f"✅ 接收到响应: {len(response)} 字节")
            print(f"响应内容: {response.hex()}")
            
            # 解析响应
            if len(response) >= 48:
                pos_response = struct.unpack("!6f", response[:24])
                ctrl_response = struct.unpack("!18B", response[24:42])
                
                print(f"响应坐标: {pos_response}")
                print(f"响应心跳: {ctrl_response[0]}")
                print(f"响应模式: 手动={ctrl_response[1]==255}, 自动={ctrl_response[2]==255}")
                print(f"响应状态: 准备={ctrl_response[3]}, 就绪={ctrl_response[4]}")
            
            sock.close()
            return True
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            try:
                sock.close()
            except:
                pass
    
    print("❌ 所有连接尝试都失败了")
    return False

if __name__ == "__main__":
    test_plc_connection()
