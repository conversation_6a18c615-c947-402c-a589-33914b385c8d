
import logging
import os
import pickle
import numpy as np
import cv2
from datetime import datetime
from server_utils.segmentation_rknn import SegmentationRKNN as Segmentation
import traceback
import threading
import func2
from rknnlite.api import RKNNLite
from server_utils.usb_camera import USBCamera

def yolo_obb(img,cy,rknn_lite):
    points,img,intersection_x=func2.main(img,rknn_lite,cy)
    if len(points) != 0:
        left_cx=intersection_x
        left_cy=cy
        left_img_vis=img
    else:
        left_cx=0
        left_cy=cy
        left_img_vis=img
        points=[]
    return left_cx, left_cy, left_img_vis,points

def log_camera_usage():
    logging.warning("Camera being initialized from:")
    for line in traceback.format_stack():
        logging.warning(line.strip())


class TubeCalibrator:
    def __init__(self, camera_params_path, model_path, config_path, out_folder, tube_calibrate_info_path, usb_camera: USBCamera):
        self.camera_params_path = camera_params_path
        self.model_path = model_path
        self.config_path = config_path
        self.out_folder = out_folder
        self.tube_calibrate_info_path = tube_calibrate_info_path
        self.localizer = self.load_model()
        self.load_camera_params()
        log_camera_usage()
        self.camera =usb_camera
        self.Y = 300
        self.rknn_lite = RKNNLite()
        ret = self.rknn_lite.load_rknn(model_path)
        if ret != 0:
            logging.error("Failed to load RKNN model.")
        ret = self.rknn_lite.init_runtime()
        if ret != 0:
            logging.error("Failed to initialize runtime environment.")

    def load_model(self):
        return Segmentation(self.model_path)

    def stop(self):
        if self.cap and self.cap.isOpened():
            self.cap.release()
            self.cap = None

    def get_current_time(self):
        current_time = datetime.now()
        return current_time.strftime('%Y-%m-%d-%H-%M-%S') + f'-{current_time.microsecond // 10000:02d}'

    def get_depth(self, disp_value):
        fx = self.mtxL[0, 0]
        baseline = np.linalg.norm(self.T)
        if disp_value > 0:
            return (fx * baseline) / disp_value
        else:
            return None

    def load_camera_params(self):
        with open(self.camera_params_path, "rb") as f:
            params = pickle.load(f)
        self.Q = params['Q']
        self.mtxL = params['mtxL']
        self.mtxR = params['mtxR']
        self.T = params['T']
        self.Left_Stereo_Map = params['Left_Stereo_Map']
        self.Right_Stereo_Map = params['Right_Stereo_Map']

    def capture_and_process(self):
        frame_pair = self.camera.capture()
        Y = self.Y
        # while True:
        # print("frame pair shape:", frame_pair.shape)
        if frame_pair is None:
            logging.error("Failed to capture frame from USBCamera")
            return None, None
        # print("frame pair shape:", frame_pair.)
        frameL, frameR = frame_pair

        # 双目矫正
        Left_nice = cv2.remap(frameL, self.Left_Stereo_Map[0], self.Left_Stereo_Map[1],
                              interpolation=cv2.INTER_LANCZOS4)
        Right_nice = cv2.remap(frameR, self.Right_Stereo_Map[0], self.Right_Stereo_Map[1],
                               interpolation=cv2.INTER_LANCZOS4)

        # self.Left_date = Left_nice
        # self.Right_date = Right_nice
        # from rknnlite.api import RKNNLite
        # rknn_lite = RKNNLite()

        # 导管检测（左右图）
        # _, left_cx, _, left_tube_contour, left_img_vis = self.localizer.main_point_localization(
        #     img=Left_nice, mode='test', cy=300)
        # _, right_cx, _, right_tube_contour, right_img_vis = self.localizer.main_point_localization(
        #     img=Right_nice, mode='test', cy=300)
        left_cx, left_cy, left_img_vis, left_tube_contour = yolo_obb(Left_nice, 300, self.rknn_lite)

        right_cx, right_cy, right_img_vis, right_tube_contour = yolo_obb(Right_nice, 300, self.rknn_lite)

        left_cy = Y
        right_cy = Y

        point3d = None
        if not None in [left_cx, left_cy, right_cx, right_cy]:
            # 计算视差和3D点
            disp = left_cx - right_cx
            depth = self.get_depth(disp)
            point3d = self.calc_3d_point(left_cx, Y, disp)
            combined_vis_img = np.hstack((left_img_vis, right_img_vis))

            # 缓存这一帧，用于后续保存
            self.Left_nice = Left_nice
            self.Right_nice = Right_nice
            self.left_cx = left_cx
            self.right_cx = right_cx
            self.Y = Y
            self.left_tube_contour = left_tube_contour
            self.right_tube_contour = right_tube_contour
            self.cached_frameL = frameL
            self.cached_frameR = frameR
            self.cached_point3d = point3d
            self.save_current_result()

            return (left_img_vis, right_img_vis), point3d
        else:
            print("未没有检测到导管，返回原始图像用于显示")
            self.Left_nice = Left_nice
            self.Right_nice = Right_nice
            self.left_cx = left_cx
            self.right_cx = right_cx
            self.Y = Y
            self.left_tube_contour = left_tube_contour
            self.right_tube_contour = right_tube_contour
            self.cached_frameL = frameL
            self.cached_frameR = frameR
            self.cached_point3d = None
            return (frameL, frameR), point3d


    def save_current_result(self):
        if self.cached_frameL is not None:
            path = self.save_result(self.cached_frameL, self.cached_frameR, self.Left_nice, self.Right_nice,
                           self.left_cx, self.Y, self.left_tube_contour,
                           self.right_cx, self.Y, self.right_tube_contour,
                           self.cached_point3d)
            # path = self.save_result(self.cached_frameL, self.cached_frameR, self.cached_point3d)
            return path
        else:
            logging.warning("No cached frame to save.")
            return None

    def process_y_value(self, y_value: int) -> bool:
        try:
            if y_value:
                self.Y = int(y_value)
            else:
                self.Y = 300  # 默认值
            logging.info(f"[TubeCalibrator] 更新 Y 值为: {self.Y}")
            return True
        except Exception as e:
            logging.error(f"[TubeCalibrator] 设置 Y 值失败: {e}")
            return False

    def save_result(self, frameL, frameR, Left_nice, Right_nice,
                  left_cx, left_cy, left_contour,
                  right_cx, right_cy, right_contour,
                  point3d):
        save_name = 'tube_calibration'
        if not os.path.exists(self.out_folder):
            os.makedirs(self.out_folder)

        cv2.imwrite(os.path.join(self.out_folder, f'{save_name}_L.png'), frameL)
        cv2.imwrite(os.path.join(self.out_folder, f'{save_name}_R.png'), frameR)
        cv2.imwrite(os.path.join(self.out_folder, f'{save_name}_LRect.png'), Left_nice)
        cv2.imwrite(os.path.join(self.out_folder, f'{save_name}_RRect.png'), Right_nice)

        tube_calibrate_info = {
            'left_cx': left_cx,
            'left_cy': left_cy,
            'left_tube_contour': left_contour,
            'right_cx': right_cx,
            'right_cy': right_cy,
            'right_tube_contour': right_contour,
            'point3d': point3d,
        }

        with open(self.tube_calibrate_info_path, "wb") as f:
            pickle.dump(tube_calibrate_info, f)

        print('导管标定参数已保存至', self.tube_calibrate_info_path)
        return self.tube_calibrate_info_path


    def calc_3d_point(self, x, y, disp):
        fx = self.mtxL[0, 0]
        fy = self.mtxL[1, 1]
        cx = self.mtxL[0, 2]
        cy = self.mtxL[1, 2]
        baseline = np.linalg.norm(self.T)
        if disp <= 0:
            return [None, None, None]
        Z = (fx * baseline) / disp
        X = (x - cx) * Z / fx
        Y = (y - cy) * Z / fy
        return [X, Y, Z]
