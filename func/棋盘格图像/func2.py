# -*- coding: utf-8 -*-
#yolo v8_obb目标检测后处理
import os
import sys
import urllib
import urllib.request
import time
import matplotlib.pyplot as plt
import numpy as np
import argparse
import cv2,math
from math import ceil
from itertools import product as product
import time 


CLASSES = ['screw']
nmsThresh = 0.1
objectThresh = 0.2

def letterbox_resize(image, size, bg_color):
    """
    letterbox_resize the image according to the specified size
    :param image: input image, which can be a NumPy array or file path
    :param size: target size (width, height)
    :param bg_color: background filling data 
    :return: processed image
    """
    if isinstance(image, str):
        image = cv2.imread(image)

    target_width, target_height = size
    image_height, image_width, _ = image.shape

    # Calculate the adjusted image size
    aspect_ratio = min(target_width / image_width, target_height / image_height)
    new_width = int(image_width * aspect_ratio)
    new_height = int(image_height * aspect_ratio)

    # Use cv2.resize() for proportional scaling
    image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)

    # Create a new canvas and fill it
    result_image = np.ones((target_height, target_width, 3), dtype=np.uint8) * bg_color
    offset_x = (target_width - new_width) // 2
    offset_y = (target_height - new_height) // 2
    result_image[offset_y:offset_y + new_height, offset_x:offset_x + new_width] = image
    return result_image, aspect_ratio, offset_x, offset_y


class DetectBox:
    def __init__(self, classId, score, xmin, ymin, xmax, ymax,angle):
        self.classId = classId
        self.score = score
        self.xmin = xmin
        self.ymin = ymin
        self.xmax = xmax
        self.ymax = ymax
        self.angle=angle

def rotate_rectangle(x1, y1, x2, y2, a):
    # 计算中心点坐标
    cx = (x1 + x2) / 2
    cy = (y1 + y2) / 2

    # 将角度转换为弧度
    # a = math.radians(a)
    # 对每个顶点进行旋转变换
    x1_new = int((x1 - cx) * math.cos(a) - (y1 - cy) * math.sin(a) + cx)
    y1_new = int((x1 - cx) * math.sin(a) + (y1 - cy) * math.cos(a) + cy)

    x2_new = int((x2 - cx) * math.cos(a) - (y2 - cy) * math.sin(a) + cx)
    y2_new = int((x2 - cx) * math.sin(a) + (y2 - cy) * math.cos(a) + cy)

    x3_new = int((x1 - cx) * math.cos(a) - (y2 - cy) * math.sin(a) + cx)
    y3_new = int((x1 - cx) * math.sin(a) + (y2 - cy) * math.cos(a) + cy)

    x4_new =int( (x2 - cx) * math.cos(a) - (y1 - cy) * math.sin(a) + cx)
    y4_new =int( (x2 - cx) * math.sin(a) + (y1 - cy) * math.cos(a) + cy)
    return [(x1_new, y1_new), (x3_new, y3_new),(x2_new, y2_new) ,(x4_new, y4_new)]


def intersection(rect1, rect2):
        # 计算两个矩形的交集
    retval, intersect_pts = cv2.rotatedRectangleIntersection(rect1, rect2)
    
    # 如果没有交集，返回0
    if retval == cv2.INTERSECT_NONE or intersect_pts is None:
        return 0.0
    
    # 计算交集面积
    intersect_pts = cv2.convexHull(intersect_pts)
    inter_area = cv2.contourArea(intersect_pts)
    
    # 获取两个矩形的顶点坐标
    box1 = cv2.boxPoints(rect1)
    box2 = cv2.boxPoints(rect2)
    
    # 计算两个矩形的面积
    area1 = cv2.contourArea(box1)
    area2 = cv2.contourArea(box2)
    
    # 计算并集面积
    union_area = area1 + area2 - inter_area
    
    # 计算IoU
    if union_area <= 0:
        return 0.0
    
    iou = inter_area / union_area
    return iou

def NMS(detectResult, nmsThresh=0.5):
    predBoxs = []

    # 按置信度排序
    sort_detectboxs = sorted(detectResult, key=lambda x: x.score, reverse=True)
    
    for i in range(len(sort_detectboxs)):
        if sort_detectboxs[i].classId == -1:
            continue  # 被抑制的框，跳过

        # 当前候选框参数
        xmin1 = sort_detectboxs[i].xmin
        ymin1 = sort_detectboxs[i].ymin
        xmax1 = sort_detectboxs[i].xmax
        ymax1 = sort_detectboxs[i].ymax
        angle1 = sort_detectboxs[i].angle
        angle1=  angle1* 180.0 / math.pi
        classId = sort_detectboxs[i].classId
        
        # 计算旋转矩形 rect1
        center_x1 = (xmin1 + xmax1) / 2
        center_y1 = (ymin1 + ymax1) / 2
        width1 = xmax1 - xmin1
        height1 = ymax1 - ymin1
        rect1 = ((center_x1, center_y1), (width1, height1), angle1)

        predBoxs.append(sort_detectboxs[i])  # 保留该框

        # 抑制与之重叠度高的框
        for j in range(i + 1, len(sort_detectboxs)):
            if sort_detectboxs[j].classId == -1:
                continue
            if sort_detectboxs[j].classId != classId:
                continue

            # 比较框参数
            xmin2 = sort_detectboxs[j].xmin
            ymin2 = sort_detectboxs[j].ymin
            xmax2 = sort_detectboxs[j].xmax
            ymax2 = sort_detectboxs[j].ymax
            angle2 = sort_detectboxs[j].angle
            angle2= angle2* 180.0 / math.pi
            center_x2 = (xmin2 + xmax2) / 2
            center_y2 = (ymin2 + ymax2) / 2
            width2 = xmax2 - xmin2
            height2 = ymax2 - ymin2
            rect2 = ((center_x2, center_y2), (width2, height2), angle2)

            # 计算旋转 IOU
            iou = intersection(rect1, rect2)
            if iou > nmsThresh:
                sort_detectboxs[j].classId = -1  # 抑制
    return predBoxs

def sigmoid(x):
    return 1 / (1 + np.exp(-x))

def softmax(x, axis=-1):
    # 将输入向量减去最大值以提高数值稳定性
    exp_x = np.exp(x - np.max(x, axis=axis, keepdims=True))
    return exp_x / np.sum(exp_x, axis=axis, keepdims=True)

def process(out,model_w,model_h,stride,angle_feature,index,scale_w=1,scale_h=1):
    class_num=len(CLASSES)
    angle_feature=angle_feature.reshape(-1)
    xywh=out[:,:64,:]
    conf=sigmoid(out[:,64:,:])
    out=[]
    conf=conf.reshape(-1)
    for ik in range(model_h*model_w*class_num):
        if conf[ik]>objectThresh:
            w=ik%model_w
            h=(ik%(model_w*model_h))//model_w
            c=ik//(model_w*model_h)
            xywh_=xywh[0,:,(h*model_w)+w] #[1,64,1]
            xywh_=xywh_.reshape(1,4,16,1)
            data=np.array([i for i in range(16)]).reshape(1,1,16,1)
            xywh_=softmax(xywh_,2)
            xywh_ = np.multiply(data, xywh_)
            xywh_ = np.sum(xywh_, axis=2, keepdims=True).reshape(-1)
            xywh_add=xywh_[:2]+xywh_[2:]
            xywh_sub=(xywh_[2:]-xywh_[:2])/2
            angle_feature_= (angle_feature[index+(h*model_w)+w]-0.25)*3.1415927410125732
            angle_feature_cos=math.cos(angle_feature_)
            angle_feature_sin=math.sin(angle_feature_)
            xy_mul1=xywh_sub[0] * angle_feature_cos
            xy_mul2=xywh_sub[1] * angle_feature_sin
            xy_mul3=xywh_sub[0] * angle_feature_sin
            xy_mul4=xywh_sub[1] * angle_feature_cos
            xy=xy_mul1-xy_mul2,xy_mul3+xy_mul4
            xywh_1=np.array([(xy_mul1-xy_mul2)+w+0.5,(xy_mul3+xy_mul4)+h+0.5,xywh_add[0],xywh_add[1]])
            xywh_=xywh_1*stride
            xmin = (xywh_[0] - xywh_[2] / 2) * scale_w
            ymin = (xywh_[1] - xywh_[3] / 2) * scale_h
            xmax = (xywh_[0] + xywh_[2] / 2) * scale_w
            ymax = (xywh_[1] + xywh_[3] / 2) * scale_h
            box = DetectBox(c,conf[ik], xmin, ymin, xmax, ymax,angle_feature_)
            out.append(box)
    return out


def main(img,rknn_lite,cy):

    # print(img.shape)
    if len(img.shape)==2:

        img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
        
    
    letterbox_img, aspect_ratio, offset_x, offset_y = letterbox_resize(img, (1280,1280), 114)  # letterbox缩放
    # plt.imshow(letterbox_img)
    # plt.show()
    infer_img = letterbox_img[..., ::-1]  # BGR2RGB

    infer_img = infer_img.reshape(1,1280,1280,3)
    
     
    # plt.imshow()
    start_time = time.time()
    
    results = rknn_lite.inference(inputs=[infer_img])
    end_time = time.time()
    elapsed_ms = (end_time - start_time) * 1000  # 转换为毫秒
    # print(f"模型耗时: {elapsed_ms:.3f} 毫秒")
    outputs=[]
    index = 0  # 用于定位 angle_feature 的起点

    for x in results[:-1]:
        h, w = x.shape[2], x.shape[3]
        if h == 160:
            stride = 8
        elif h == 80:
            stride = 16
        elif h == 40:
            stride = 32
        else:
            continue  # 跳过不识别的尺寸
        feature = x.reshape(1, 65, -1)  # 把 [1, 66, h, w] 转成 [1, 66, h*w]
        output = process(feature, w, h, stride, results[-1], index)
        outputs += output
        index += h * w  # 每次用完之后更新 index，供下一个尺度使用
    # for x in results[:-1]:
    #     index,stride=0,0
        # if x.shape[2]==20:
        #     stride=32
        #     index=20*4*20*4+20*2*20*2
        # if x.shape[2]==40:
        #     stride=16
        #     index=20*4*20*4
        # if x.shape[2]==80:
        #     stride=8
        #     index=0
    #     print(x.shape)
    #     feature=x.reshape(1,66,-1)
    #     output=process(feature,x.shape[3],x.shape[2],stride,results[-1],index)
    #     outputs=outputs+
    start_time = time.time()
    
    predbox = NMS(outputs)
    end_time = time.time()
    elapsed_ms = (end_time - start_time) * 1000  # 转换为毫秒
    # print(f"nms耗时: {elapsed_ms:.3f} 毫秒")
   
    #predbox = outputs
    target=[]
    rescore=0

    for index in range(len(predbox)):
        xmin = int((predbox[index].xmin-offset_x)/aspect_ratio)
        ymin = int((predbox[index].ymin-offset_y)/aspect_ratio)
        xmax = int((predbox[index].xmax-offset_x)/aspect_ratio)
        ymax = int((predbox[index].ymax-offset_y)/aspect_ratio)
        classId = predbox[index].classId
        score = predbox[index].score
        if score>rescore:
            angle = predbox[index].angle
            points=rotate_rectangle(xmin,ymin,xmax,ymax,angle)
            #cv2.polylines(img, [np.asarray(points, dtype=int)], True,  (0, 255, 0), 1)
            contour =np.array([points[0], points[1], points[2], points[3]])
            
    # reshape 成符合 OpenCV 要求的格式：(n, 1, 2)
            contour = contour.reshape(-1, 1, 2)
            target=contour
            rescore=score
    if len(target) != 0:
        # 2. 绘制旋转矩形轮廓（黄色边框）
        cv2.drawContours(img, [target], -1, (255, 255, 0), 2)
        height, width = img.shape[:2]
        # 3. 计算旋转矩形的长轴中心线
        rect = cv2.minAreaRect(target)  # 返回 ((中心x, 中心y), (宽, 高), 旋转角度)
        (center_x, center_y), (w, h), angle = rect

        # 确定长轴方向（如果宽度 > 高度，则角度不变；否则旋转90度）
        if w > h:
            major_angle = angle
        else:
            major_angle = angle + 90

        angle_rad = np.deg2rad(major_angle)

        # 延长长度（保证足够大，可穿越图像边缘）
        L = max(width, height) * 2

        # 从中心向两个方向延伸
        end1_x = int(center_x + L * np.cos(angle_rad))
        end1_y = int(center_y + L * np.sin(angle_rad))
        end2_x = int(center_x - L * np.cos(angle_rad))
        end2_y = int(center_y - L * np.sin(angle_rad))
        # 4. 绘制长轴中心线（红色）
        cv2.line(img, (end1_x, end1_y), (end2_x, end2_y), (0, 0, 255), 2)

        # 5. 求长轴中心线与水平线 y=300 的交点
        # 长轴中心线的斜截式方程：y = kx + b
        if end1_x != end2_x:  # 避免除以零（垂直线）
            k = (end2_y - end1_y) / (end2_x - end1_x)  # 斜率
            b = end1_y - k * end1_x  # 截距

            # 求与 y=300 的交点
            intersect_x = (cy - b) / k
            intersect_y = cy

            # 检查交点是否在图像范围内
            if 0 <= intersect_x <= width and 0 <= intersect_y <= height:
                intersect_point = (int(intersect_x), int(intersect_y))
                # print(f"交点坐标: {intersect_point}")

                # 绘制交点（绿色圆点）
                cv2.circle(img, intersect_point, 5, (0, 255, 0), -1)
            else:
                1
                # print("交点在图像外")
        else:  # 长轴是垂直线（斜率无穷大）
            if 0 <= center_x <= width:
                intersect_point = (int(center_x), cy)
                # print(f"交点坐标: {intersect_point}")
                cv2.circle(img, intersect_point, 5, (0, 255, 0), -1)
            else:
                # print("交点在图像外")
                1

        # 6. 绘制水平线 y=300（蓝色）
        cv2.line(img, (0, cy), (width, cy), (255, 0, 0), 2)
    else:
        intersect_point=[0,0]
    # plt.imshow(img)
    # plt.show()
    return target,img,intersect_point[0]
        #ptext = (xmin, ymin)
        #title= CLASSES[classId] + "%.2f" % score
    # print(type(target))
    
      #  cv2.putText(img, title, ptext, cv2.FONT_HERSHEY_DUPLEX, 0.5, (0, 0, 255), 1)
       # plt.imshow(img)
       # plt.show()

   

