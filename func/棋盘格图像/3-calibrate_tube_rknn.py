# Package importation
import glob
import os.path
import func2
import numpy as np
import cv2
import pickle
from datetime import datetime
import matplotlib.pyplot as plt

import math
# Filtering
# kernel = np.ones((3, 3), np.uint8)
# chessboard_size = (8,5)

def yolo_obb(img,cy,rknn_lite):
    points,img,intersection_x=func2.main(img,rknn_lite,cy)
    if len(points) != 0:
        left_cx=intersection_x
        left_cy=cy
        left_img_vis=img
    else:
        left_cx=0
        left_cy=cy
        left_img_vis=img
        points=[]
    return left_cx, left_cy, left_img_vis,points
 
def get_current_time():
    # 获取当前时间
    current_time = datetime.now()

    # 格式化时间戳
    timestamp = current_time.strftime('%Y-%m-%d-%H-%M-%S') + f'-{current_time.microsecond // 10000:02d}'

    print(timestamp)
    return timestamp

# def get_depth(x1, x2, mtxL, T, Q):
def get_depth(disp_value, mtxL, T):

    # x1,x2 = 535, 161
    # disp_value = x1 - x2
    # print('disp_value',disp_value)

    # 计算焦距 fx 和基线 B
    fx = mtxL[0, 0]  # 相机内参矩阵中的焦距 fx
    baseline = np.linalg.norm(T)  # 基线长度是 T 向量的模

    # 计算实际深度
    if disp_value > 0:  # 确保视差值有效
        depth = (fx * baseline) / disp_value  # 深度计算公式
    else:
        depth = None  # 如果视差为0，表示该像素在图像平面上无法计算深度
    # print(f">>> Depth is {depth} mm")
    return depth

def calc_3d_point(x, y, disp_value, intrinsic_k, T):
    fx = intrinsic_k[0, 0]
    fy = intrinsic_k[1, 1]
    cx = intrinsic_k[0, 2]
    cy = intrinsic_k[1, 2]
    baseline = np.linalg.norm(T)

    X_3D, Y_3D, Z = None,None,None

    # 计算深度 Z
    if disp_value > 0:  # 避免除零
        Z = (fx * baseline) / disp_value
        # 计算3D坐标
        X_3D = (x - cx) * Z / fx
        Y_3D = (y - cy) * Z / fy


    return [X_3D, Y_3D, Z]


def main(camera_params_path, out_folder, localizer,tube_calibrate_info_path):

 
    from rknnlite.api import RKNNLite
    rknn_lite = RKNNLite()
    # Load the model
    ret = rknn_lite.load_rknn("/root/Desktop/vv/yolov8_obb.rknn")
    if ret != 0:
        print("Failed to load RKNN model.")
    # Initialize the runtime
    ret = rknn_lite.init_runtime()
    if ret != 0:
        print("Failed to initialize runtime environment.")
    print("Model loaded and runtime initialized successfully.")
    # model = YOLO(r"/root/Desktop/vv/best.pt") 
    print('开始标定...请保持选中Main窗口界面，按S键保存导管标定结果')
    with open(camera_params_path, "rb") as f:
        camera_params = pickle.load(f)

    Q = camera_params['Q']
    mtxL = camera_params['mtxL']
    mtxR = camera_params['mtxR']
    T = camera_params['T']
    Left_Stereo_Map = camera_params['Left_Stereo_Map']
    Right_Stereo_Map = camera_params['Right_Stereo_Map']
    depth = None
    point3d = None
    Y = 300

    # print('Q=',Q)
    # print('mtxL=',mtxL)
    # print('T=',T)
    # print('Left_Stereo_Map=',Left_Stereo_Map)
    # print('Right_Stereo_Map=',Right_Stereo_Map)

    # ***** Starting the StereoVision *****

    # Call the two cameras
    camera = cv2.VideoCapture(1)
    camera.set(cv2.CAP_PROP_FRAME_WIDTH,2560)
    camera.set(cv2.CAP_PROP_FRAME_HEIGHT,720)

    while True:
    # for i in range(1):
        # # Start Reading Camera images
        ####
        ret, frame = camera.read()
        if not ret:
            print('未找到相机.')
            exit()
        frameL = frame[0:720, 0:1280]
        frameR = frame[0:720, 1280:2560]
   
        ####

        ####
        # left_path = r'D:\Projects\Local_projects\demo_project\tube_changeclip_stereovision\test_img\3_mask\2024-11-14-17-38-59-37_L.png'
        # right_path = r'D:\Projects\Local_projects\demo_project\tube_changeclip_stereovision\test_img\3_mask\2024-11-14-17-38-59-37_R.png'
        # frameL = cv2.imread(left_path)
        # frameR = cv2.imread(right_path)
        ####
        
        # Rectify the images on rotation and alignement

        Left_nice = cv2.remap(frameL, Left_Stereo_Map[0], Left_Stereo_Map[1], interpolation=cv2.INTER_LANCZOS4,
                              borderMode=cv2.BORDER_CONSTANT)  # Rectify the image using the kalibration parameters founds during the initialisation
        
        Right_nice = cv2.remap(frameR, Right_Stereo_Map[0], Right_Stereo_Map[1], interpolation=cv2.INTER_LANCZOS4,
                               borderMode=cv2.BORDER_CONSTANT)
       
        
        left_cx, left_cy,  left_img_vis,left_tube_contour = yolo_obb(Left_nice,300,rknn_lite)
                                                                                                 
        right_cx, right_cy,  right_img_vis,right_tube_contour =yolo_obb(Right_nice,300,rknn_lite)

        left_cy = Y
        right_cy = Y

        if not None in [left_cx,left_cy,right_cx,right_cy]:
            # print(f'left_cx={left_cx}, left_cy={left_cy}')
            # print(f'right_cx={right_cx}, right_cy={right_cy}')

            disp_value = left_cx - right_cx
            depth = get_depth(disp_value, mtxL, T)

            # print(f">>> Depth is {depth} mm")

            point3d = calc_3d_point(left_cx, left_cy, disp_value, mtxL, T)
            #print(f">>> 导管位置为 {point3d}")
            # print()
    ########
        # cv2.imshow('Left_nice', Left_nice)
        # cv2.imshow('Right_nice', Right_nice)
        
        combined_vis_img = np.hstack((left_img_vis, right_img_vis))
        #cv2.imshow('Original', frame)
        #cv2.imshow('Main', combined_vis_img)
        cv2.namedWindow('left_img_vis', cv2.WINDOW_NORMAL)  # WINDOW_NORMAL允许调整窗口大小
        cv2.namedWindow('right_img_vis', cv2.WINDOW_NORMAL)

        # 设置窗口大小（宽度和高度，单位为像素）
        cv2.resizeWindow('left_img_vis', 400, 300)  # 设置为800x600像素
        cv2.resizeWindow('right_img_vis', 400, 300)

        cv2.imshow('left_img_vis', left_img_vis)
        cv2.imshow('right_img_vis', right_img_vis)
        # cv2.imshow('left_img_vis', frameL)
        # cv2.imshow('right_img_vis', frameR)

        if cv2.waitKey(1) & 0xFF == ord('S'):
            print('\n>>> 保存导管标定参数中...')
            save_name = 'tube_calibration' #get_current_time()
            left_name = save_name+'_L'
            right_name = save_name+'_R'
            left_Rect_name = save_name+'_LRect'
            right_Rect_name = save_name+'_RRect'

            cv2.imencode('.png', frameL)[1].tofile(os.path.join(out_folder, f'{left_name}.png'))
            cv2.imencode('.png', frameR)[1].tofile(os.path.join(out_folder, f'{right_name}.png'))
            cv2.imencode('.png', Left_nice)[1].tofile(os.path.join(out_folder, f'{left_Rect_name}.png'))
            cv2.imencode('.png', Right_nice)[1].tofile(os.path.join(out_folder, f'{right_Rect_name}.png'))

            tube_calibrate_info = {
                'left_cx':left_cx,
                'left_cy':left_cy,
                'left_tube_contour':left_tube_contour,
                'right_cx':right_cx,
                'right_cy':right_cy,
                'right_tube_contour':right_tube_contour,
                # 'depth':depth,
                'point3d':point3d,
            }
            print(f">>> 导管位置为 {point3d}")
            if None in point3d:
                print(f'>>> 当前保存的导管位置为空，请注意')
            # print('导管标定参数：\n', tube_calibrate_info)

            with open(tube_calibrate_info_path, "wb") as f:
                pickle.dump(tube_calibrate_info, f)
            print('>>> 导管标定参数已保存至', tube_calibrate_info_path)
        # End the Programme
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break


    # Release the Cameras
    # camera.release()
    cv2.destroyAllWindows()

if __name__ == '__main__':

    print('>>> 正在加载环境...')
    # camera_params_path = r'D:\Projects\Local_projects\demo_project\tube_changeclip_stereovision\calibration_configs\camera_params.pkl'
    camera_params_path = r'/root/Desktop/vv/标定参数/相机参数.pkl'
    model_path = r'segmentation\models\changeclip_tube_segmentation_20241120.pdparams'
    config_path = r'segmentation\models\changeclip_tube_segmentation_20241120.yml'
    out_folder = r'标定参数'
    tube_calibrate_info_path = os.path.join(out_folder, '导管参数.pkl')
   
    main(camera_params_path=camera_params_path, out_folder=out_folder, localizer=0,tube_calibrate_info_path=tube_calibrate_info_path)


