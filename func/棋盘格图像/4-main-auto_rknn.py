# Package importation
import glob
import os.path
import func2
import cv2
import math
from PIL import Image
import numpy as np
import cv2
import pickle
from datetime import datetime
import time
import matplotlib.pyplot as plt
import torch
# Filtering
kernel = np.ones((3, 3), np.uint8)
chessboard_size = (8,6)

def get_current_time():
    # 获取当前时间
    current_time = datetime.now()

    # 格式化时间戳
    timestamp = current_time.strftime('%Y-%m-%d-%H-%M-%S') + f'-{current_time.microsecond // 10000:02d}'

    print(timestamp)
    return timestamp

def get_depth(disp_value, mtxL, T):

    # print('disp_value',disp_value)

    # 计算焦距 fx 和基线 B
    fx = mtxL[0, 0]  # 相机内参矩阵中的焦距 fx
    baseline = np.linalg.norm(T)  # 基线长度是 T 向量的模

    # 计算实际深度
    if disp_value > 0:  # 确保视差值有效
        depth = (fx * baseline) / disp_value  # 深度计算公式
    else:
        depth = None  # 如果视差为0，表示该像素在图像平面上无法计算深度
    return depth
def yolo_obb(img,cy,rknn_lite):
    points,img,intersection_x=func2.main(img,rknn_lite,cy)
    if len(points) != 0:
        left_cx=intersection_x
        left_cy=cy
        left_img_vis=img
    else:
        left_cx=0
        left_cy=cy
        left_img_vis=img
        points=[]
    return left_cx, left_cy, left_img_vis,points
  
def calc_3d_point(x, y, disp_value, intrinsic_k, T):
    fx = intrinsic_k[0, 0]
    fy = intrinsic_k[1, 1]
    cx = intrinsic_k[0, 2]
    cy = intrinsic_k[1, 2]
    baseline = np.linalg.norm(T)

    X_3D, Y_3D, Z = None,None,None

    # 计算深度 Z
    if disp_value > 0:  # 避免除零
        Z = (fx * baseline) / disp_value
        # 计算3D坐标
        X_3D = (x - cx) * Z / fx
        Y_3D = (y - cy) * Z / fy

    # print(f">>> points_3D is X {X_3D}, Y {Y_3D}, Z {Z}")
    return [X_3D, Y_3D, Z]


def main(camera_params_path, out_folder, localizer,tube_calibrate_info_path):
    from rknnlite.api import RKNNLite
    rknn_lite = RKNNLite()
    # Load the model
    ret = rknn_lite.load_rknn("/root/Desktop/vv/yolov8_obb.rknn")
    if ret != 0:
        print("Failed to load RKNN model.")
    # Initialize the runtime
    ret = rknn_lite.init_runtime()
    if ret != 0:
        print("Failed to initialize runtime environment.")
    print("Model loaded and runtime initialized successfully.")
    # model = YOLO(r"/root/Desktop/vv/best.pt") 
    print('开始标定...请保持选中Main窗口界面，按S键保存导管标定结果')
    out_left_folder = os.path.join(out_folder, 'left')
    out_right_folder = os.path.join(out_folder, 'right')
    out_leftR_folder = os.path.join(out_folder, 'leftR')
    out_rightR_folder = os.path.join(out_folder, 'rightR')
    for folder in [out_left_folder, out_right_folder,out_leftR_folder,out_rightR_folder]:
        if not os.path.isdir(folder):
            os.makedirs(folder)

    with open(camera_params_path, "rb") as f:
        camera_params = pickle.load(f)

    Q = camera_params['Q']
    mtxL = camera_params['mtxL']
    mtxR = camera_params['mtxR']
    T = camera_params['T']
    Left_Stereo_Map = camera_params['Left_Stereo_Map']
    Right_Stereo_Map = camera_params['Right_Stereo_Map']

    with open(tube_calibrate_info_path, "rb") as f:
        tube_params = pickle.load(f)
    tube_left_cx =tube_params['left_cx']
    tube_left_cy =tube_params['left_cy']
    tube_right_cx =tube_params['right_cx']
    tube_right_cy =tube_params['right_cy']
    tube_right_cy =tube_params['right_cy']
    # tube_depth = tube_params['depth']
    tube_point3d = tube_params['point3d']
    
    print(f"\n>>> 导入导管参数... 导管标定位置为 {tube_point3d}")
    if tube_point3d is None:
        print(f">>> 缺少导管标定信息，请重新确认")
        return

    camera = cv2.VideoCapture(0
                              )
    camera.set(cv2.CAP_PROP_FRAME_WIDTH,2560)
    camera.set(cv2.CAP_PROP_FRAME_HEIGHT,720)

    while True:
    # for i in range(0):
        # # Start Reading Camera images
        
        ret, frame = camera.read()
        if not ret:
            print('Camera not detected. Exit')
            exit()

        frameL = frame[0:720, 0:1280]
        frameR = frame[0:720, 1280:2560]

        Left_nice = cv2.remap(frameL, Left_Stereo_Map[0], Left_Stereo_Map[1], interpolation=cv2.INTER_LANCZOS4,
                              borderMode=cv2.BORDER_CONSTANT)  # Rectify the image using the kalibration parameters founds during the initialisation
        Right_nice = cv2.remap(frameR, Right_Stereo_Map[0], Right_Stereo_Map[1], interpolation=cv2.INTER_LANCZOS4,
                               borderMode=cv2.BORDER_CONSTANT)

        
        left_cx, left_cy,  left_img_vis,left_tube_contour = yolo_obb(Left_nice,300,rknn_lite)
                                                                                                 
        right_cx, right_cy,  right_img_vis,right_tube_contour =yolo_obb(Right_nice,300,rknn_lite)


        cv2.namedWindow('Left', cv2.WINDOW_NORMAL)
        cv2.namedWindow('Right', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('Left', 800, 600) 
        cv2.resizeWindow('Right', 800, 600) 
        cv2.imshow('Left', left_img_vis)
        cv2.imshow('Right', right_img_vis)

        if cv2.waitKey(1) & 0xFF == ord('A'):
            if not None in [left_cx, left_cy, right_cx, right_cy]:
                print('>>> 已检测到物体')
                disp_value = left_cx - right_cx
                # current_depth = get_depth(disp_value, mtxL, T)
                current_point3d = calc_3d_point(left_cx, left_cy, disp_value, mtxL, T)
                print(f">>> 导管标定位置 {tube_point3d}")
                print(f">>> 当前导管位置 {current_point3d}")
                if None in current_point3d:
                    print('无法计算有效的当前导管位置\n')

                else:
                    X_difference = tube_point3d[0] - current_point3d[0]
                    Z_difference = tube_point3d[2] - current_point3d[2]
                    print(f">>> 左右偏移量= {X_difference} mm\n>>> 前后偏移量= {Z_difference} mm\n")

            else:
                print('>>> 未检测到物体\n')
 
        
        #if cv2.waitKey(1) & 0xFF == ord('S'):
            print('\n>>> 保存图片中...')
            save_name = get_current_time()
            left_name = save_name+'_L'
            right_name = save_name+'_R'
            left_Rect_name = save_name+'_LRect'
            right_Rect_name = save_name+'_RRect'
            #
            cv2.imwrite(os.path.join(out_left_folder, f'{left_name}.png'), frameL)
            cv2.imwrite(os.path.join(out_right_folder, f'{right_name}.png'), frameR)
            cv2.imwrite(os.path.join(out_leftR_folder, f'{left_Rect_name}.png'), Left_nice)
            cv2.imwrite(os.path.join(out_rightR_folder, f'{right_Rect_name}.png'), Right_nice)

    camera.release()
    cv2.destroyAllWindows()

if __name__ == '__main__':

    print('>>> 正在加载环境...')
    camera_params_path = r'标定参数\相机参数.pkl'
    model_path = r'segmentation\models\changeclip_tube_segmentation_20241120.pdparams'
    config_path =r'segmentation\models\changeclip_tube_segmentation_20241120.yml'
    out_folder = r'results'
    tube_calibrate_info_path = r'标定参数\导管参数.pkl'
    
    main(camera_params_path=camera_params_path,out_folder=out_folder, localizer=1, tube_calibrate_info_path=tube_calibrate_info_path)


