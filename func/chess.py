import os
import pickle
import numpy as np
import cv2
from datetime import datetime
from server_utils.segmentation_rknn import SegmentationRKNN as Segmentation


class TubeCalibrator:
    def __init__(self, camera_params_path,  out_folder, tube_calibrate_info_path):
        self.camera_params_path = camera_params_path
        # self.model_path = model_path
        # self.config_path = config_path
        self.out_folder = out_folder
        self.tube_calibrate_info_path = tube_calibrate_info_path
        self.localizer = Segmentation(model_path, config_path)

        self.camera = None
        self.load_camera_params()

    def load_camera_params(self):
        with open(self.camera_params_path, "rb") as f:
            params = pickle.load(f)
        self.Q = params['Q']
        self.mtxL = params['mtxL']
        self.mtxR = params['mtxR']
        self.T = params['T']
        self.Left_Stereo_Map = params['Left_Stereo_Map']
        self.Right_Stereo_Map = params['Right_Stereo_Map']

    def get_current_time(self):
        current_time = datetime.now()
        return current_time.strftime('%Y-%m-%d-%H-%M-%S') + f'-{current_time.microsecond // 10000:02d}'

    def get_depth(self, disp_value):
        fx = self.mtxL[0, 0]
        baseline = np.linalg.norm(self.T)
        if disp_value > 0:
            return (fx * baseline) / disp_value
        else:
            return None

    def calc_3d_point(self, x, y, disp_value):
        fx = self.mtxL[0, 0]
        fy = self.mtxL[1, 1]
        cx = self.mtxL[0, 2]
        cy = self.mtxL[1, 2]
        baseline = np.linalg.norm(self.T)
        if disp_value <= 0:
            return [None, None, None]
        Z = (fx * baseline) / disp_value
        X = (x - cx) * Z / fx
        Y = (y - cy) * Z / fy
        return [X, Y, Z]

    def run(self):
        print('开始标定...请保持选中Main窗口界面，按S键保存导管标定结果')
        self.camera = cv2.VideoCapture(0)
        self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

        Y = 300
        while True:
            ret, frame = self.camera.read()
            if not ret:
                print('未找到相机.')
                break

            frameL = frame[0:480, 0:640]
            frameR = frame[0:480, 640:1280]

            # 校正图像
            Left_nice = cv2.remap(frameL, self.Left_Stereo_Map[0], self.Left_Stereo_Map[1],
                                  interpolation=cv2.INTER_LANCZOS4, borderMode=cv2.BORDER_CONSTANT)
            Right_nice = cv2.remap(frameR, self.Right_Stereo_Map[0], self.Right_Stereo_Map[1],
                                   interpolation=cv2.INTER_LANCZOS4, borderMode=cv2.BORDER_CONSTANT)

            _, left_cx, _, left_tube_contour, left_img_vis = self.localizer.main_point_localization(
                img=Left_nice, mode='test', cy=Y)
            _, right_cx, _, right_tube_contour, right_img_vis = self.localizer.main_point_localization(
                img=Right_nice, mode='test', cy=Y)

            point3d = None
            if None not in [left_cx, right_cx]:
                disp_value = left_cx - right_cx
                depth = self.get_depth(disp_value)
                point3d = self.calc_3d_point(left_cx, Y, disp_value)

            combined_vis_img = np.hstack((left_img_vis, right_img_vis))
            # cv2.imshow('Main', combined_vis_img)

            key = cv2.waitKey(1) & 0xFF
            if key == ord('s'):
                self.save_data(frameL, frameR, Left_nice, Right_nice,
                               left_cx, Y, left_tube_contour,
                               right_cx, Y, right_tube_contour,
                               point3d)
            elif key == ord('q'):
                break

        self.camera.release()
        cv2.destroyAllWindows()

    def save_data(self, frameL, frameR, Left_nice, Right_nice,
                  left_cx, left_cy, left_contour,
                  right_cx, right_cy, right_contour,
                  point3d):
        save_name = 'tube_calibration'
        if not os.path.exists(self.out_folder):
            os.makedirs(self.out_folder)

        cv2.imwrite(os.path.join(self.out_folder, f'{save_name}_L.png'), frameL)
        cv2.imwrite(os.path.join(self.out_folder, f'{save_name}_R.png'), frameR)
        cv2.imwrite(os.path.join(self.out_folder, f'{save_name}_LRect.png'), Left_nice)
        cv2.imwrite(os.path.join(self.out_folder, f'{save_name}_RRect.png'), Right_nice)

        tube_calibrate_info = {
            'left_cx': left_cx,
            'left_cy': left_cy,
            'left_tube_contour': left_contour,
            'right_cx': right_cx,
            'right_cy': right_cy,
            'right_tube_contour': right_contour,
            'point3d': point3d,
        }

        with open(self.tube_calibrate_info_path, "wb") as f:
            pickle.dump(tube_calibrate_info, f)

        print('导管标定参数已保存至', self.tube_calibrate_info_path)


if __name__ == '__main__':
    camera_params_path = r'D:\orangepi_server\configs\camera_params.pkl'
    # model_path = r'segmentation\models_changeclip\changeclip_tube_segmentation_20241120.pdparams'
    # config_path = r'C:\Users\<USER>\Desktop\test_UI\segmentation\models_changeclip\changeclip_tube_segmentation_20241120.yml'
    out_folder = r'标定参数'
    tube_calibrate_info_path = r'C:\Users\<USER>\Desktop\test_UI\tube_config\tube_config_0.pkl'

    calibrator = TubeCalibrator(camera_params_path, model_path, config_path, out_folder, tube_calibrate_info_path)
    calibrator.run()
