# func/chessboard.py
import logging
import os
import threading
import cv2
import numpy as np
import json
import time
from PIL import Image
from queue import Queue, Empty
from server_utils.usb_camera import USBCamera

with open("configs/config.json", "r", encoding="utf-8") as f:
    config = json.load(f)


class ChessboardCaptureThread:
    def __init__(self, usb_camera: USBCamera):
        self.cap = None  # 支持传入外部摄像头实例
        self.running = False
        self.save_next_frame = False
        self.frame_queue = Queue(maxsize=1)
        self.current_frame = None
        self.left_path = ""
        self.right_path = ""
        self.initialized = False
        self.latest_raw_frame = None
        self.chess = usb_camera  # 在 __init__ 中创建一次
        self.paused_after_detection = False  # 角点检测到后暂停等待指令
        self.last_detected_frame = None  # 带角点的图像对

        # 设置参数...
        base_dir = os.path.dirname(os.path.abspath(__file__))
        self.root_path = config["calibration_config"]["chessboard_image_root"]
        self.left_folder = os.path.join(self.root_path, '左')
        self.right_folder = os.path.join(self.root_path, '右')
        self.cache_dir = config["calibration_config"]["cache_dir"]
        self.id_image = 0
        self.chessboard_size = (8, 6)
        self.criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)

        # 创建目录...
        for folder in [self.left_folder, self.right_folder]:
            if not os.path.isdir(folder):
                os.makedirs(folder)

    def start(self):
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self._capture_loop, daemon=True)
            self.thread.start()

    def _capture_loop(self):

        while self.running:
            if self.paused_after_detection:
                # 检测到角点后暂停，不再采集新图像
                time.sleep(0.1)
                continue

            frame_pair = self.chess.capture()
            if frame_pair is None:
                logging.warning("无法读取帧")
                continue
            logging.warning("--开始采集棋盘格图像--")
            frameL, frameR = frame_pair

            self.latest_raw_frame = (frameL.copy(), frameR.copy())
            threading.Thread(target=self._detect_chessboard_async, args=(frameL.copy(), frameR.copy()),
                             daemon=True).start()
            imshow_frameL = frameL.copy()
            imshow_frameR = frameR.copy()
            cv2.putText(imshow_frameL, 'L', (5, 40), cv2.FONT_HERSHEY_PLAIN, 2, (0, 255, 0), 2)
            cv2.putText(imshow_frameR, 'R', (5, 40), cv2.FONT_HERSHEY_PLAIN, 2, (0, 255, 0), 2)
            combined_imshow = np.hstack((imshow_frameL, imshow_frameR))

            try:
                self.frame_queue.put_nowait((frameL, frameR))  # 实时图像
            except:
                pass
            cv2.waitKey(1)
        if self.cap and self.cap.isOpened():
            self.cap.release()
        cv2.destroyAllWindows()
        # self.cap.release()
        cv2.destroyAllWindows()

    def _detect_chessboard_async(self, frameL, frameR):
        grayL = cv2.cvtColor(frameL, cv2.COLOR_BGR2GRAY)
        grayR = cv2.cvtColor(frameR, cv2.COLOR_BGR2GRAY)
        retL, cornersL = cv2.findChessboardCorners(grayL, self.chessboard_size, None)
        retR, cornersR = cv2.findChessboardCorners(grayR, self.chessboard_size, None)
        if retL and retR:
            cornersL = cv2.cornerSubPix(grayL, cornersL, (11, 11), (-1, -1), self.criteria)
            cornersR = cv2.cornerSubPix(grayR, cornersR, (11, 11), (-1, -1), self.criteria)
            grayL_vis = cv2.cvtColor(grayL, cv2.COLOR_GRAY2BGR)
            grayR_vis = cv2.cvtColor(grayR, cv2.COLOR_GRAY2BGR)
            cv2.drawChessboardCorners(grayL_vis, self.chessboard_size, cornersL, retL)
            cv2.drawChessboardCorners(grayR_vis, self.chessboard_size, cornersR, retR)
            self.last_detected_frame = (grayL_vis, grayR_vis)
            self.current_frame = (frameL.copy(), frameR.copy())
            self.save(grayL_vis, grayR_vis)
            self.paused_after_detection = True
            logging.info("角点检测成功，暂停采集，等待客户端确认")

    def save(self, grayL_vis, grayR_vis):
        os.makedirs(self.cache_dir, exist_ok=True)
        left_path = os.path.join(self.cache_dir, "left.jpg")
        right_path = os.path.join(self.cache_dir, "right.jpg")
        success_left = cv2.imwrite(left_path, grayL_vis)
        success_right = cv2.imwrite(right_path, grayR_vis)
        if not success_left or not success_right:
            print(f"[错误] 缓存图像写入失败：left={success_left}, right={success_right}")

    def _save_frame_pair(self):
        import os
        import cv2

        os.makedirs("./cache", exist_ok=True)
        os.makedirs(self.left_folder, exist_ok=True)
        os.makedirs(self.right_folder, exist_ok=True)

        temp_left_path = os.path.join(self.cache_dir, "left.jpg")
        temp_right_path = os.path.join(self.cache_dir, "right.jpg")
        # 🔧 修改：软件启动时从0开始，运行期间递增，重启后覆盖
        if not hasattr(self, 'id_image'):
            self.id_image = 0  # 软件启动时从0开始
            print(f"[信息] 软件启动，图像编号从 {self.id_image} 开始（覆盖模式）")

        # 路径准备
        left_save_path = os.path.join(self.left_folder, f'chessboard-L{self.id_image}.png')
        right_save_path = os.path.join(self.right_folder, f'chessboard-R{self.id_image}.png')

        print(f'--- 正在保存第 {self.id_image} 张图像 ---\n左图地址：{left_save_path}\n右图地址：{right_save_path}\n')

        # 读取缓存图像
        left_img = cv2.imread(temp_left_path)
        right_img = cv2.imread(temp_right_path)

        if left_img is None or right_img is None:
            print("[错误] 未能读取缓存图像。请确认是否已生成 left.jpg / right.jpg")
            return

        # 保存图像
        cv2.imwrite(left_save_path, left_img)
        cv2.imwrite(right_save_path, right_img)

        # 更新状态
        self.left_path = left_save_path
        self.right_path = right_save_path
        self.id_image += 1

        print(f'--- ✅ 棋盘格图像保存成功 ---')

    def get_latest_raw_frame(self):
        """无论是否检测到棋盘格都返回最新一帧"""
        return self.latest_raw_frame

    def get_latest_frames(self):
        """返回检测到的角点图像（暂停期间），否则返回 None"""
        return self.last_detected_frame

    def resume(self):
        logging.info("[CameraCapture] Resume called, releasing pause")
        # self._pause_event.set()
        self.paused_after_detection = False

    def save_current_frame(self):
        """供外部调用保存当前帧"""
        try:
            self._save_frame_pair()
            return 1
        except Exception as e:
            print(f"保存图像时出错: {e}")
            return 0

    def stop(self, release_cap=True):
        self.running = False
        cv2.destroyAllWindows()


