# Package importation
import glob
import json
import os.path

import numpy as np
import cv2
from openpyxl import Workbook  # Used for writing data into an Excel file
# from sklearn.preprocessing import normalize
import pickle

"""
retR: 返还的重投影误差，用来衡量相机标定结果的准确度。
mtxR: 相机的内参矩阵，包含焦距和主点坐标。
distR: 相机的畸变系数，包括径向畸变和切向畸变。
rvecsR: 每张图像的旋转向量，描述从世界坐标系到相机坐标系的旋转。
tvecsR: 每张图像的平移向量，描述从世界坐标系到相机坐标系的平移。
"""

camera_calibration_config = {}

def init_termination_criteria(max_iter=30, epsilon=0.001):
    criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, max_iter, epsilon)
    criteria_stereo = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, max_iter, epsilon)
    return criteria, criteria_stereo

def init_chessboard_points(row=8, col=6,square_size=25):
    objp = np.zeros((row * col, 3), np.float32)
    # objp[:, :2] = np.mgrid[0:row, 0:col].T.reshape(-1, 2)
    objp[:, :2] = np.mgrid[0:row, 0:col].T.reshape(-1, 2) * square_size
    return objp

def calibrate_camera(left_img_path,right_img_path,camera_config_path, chessboard_size=(8, 6), square_size=25, save_backup=True):

    print('开始标定...')
    list_path_left_imgs = glob.glob(os.path.join(left_img_path, '*.png'))
    list_path_right_imgs = glob.glob(os.path.join(right_img_path, '*.png'))

    list_path_left_imgs = list(sorted(list_path_left_imgs, key=lambda x: int(x.split('.png')[0].split('-L')[-1])))
    list_path_right_imgs = list(sorted(list_path_right_imgs, key=lambda x: int(x.split('.png')[0].split('-R')[-1])))
    criteria, criteria_stereo = init_termination_criteria(max_iter=30, epsilon=0.001)
    pattern_size=(8,6)
    # Prepare object points
    objp = init_chessboard_points(row=8, col=6)

    # Arrays to store object points and image points from all images
    objpoints = []  # 3d points in real world space
    imgpointsR = []  # 2d points in image plane
    imgpointsL = []

    # ChessImaR = None
    # if ChessImaR is None:
    #     return

    # Start calibration from the camera
    print('Starting calibration for the 2 cameras... ')
    # Call all saved images
    for i, image_path in enumerate(zip(list_path_left_imgs, list_path_right_imgs)):
        print(f'寻找棋盘格角点中... 进度: {i+1}/{len(list_path_left_imgs)}')
        left_path, right_path = image_path
        # print('left_path',left_path)
        # print('right_path',right_path)
        print()
        ChessImaR = cv2.imdecode(np.fromfile(right_path, np.uint8), cv2.IMREAD_GRAYSCALE)  # Right side
        ChessImaL = cv2.imdecode(np.fromfile(left_path, np.uint8), cv2.IMREAD_GRAYSCALE)  # Left side

        retR, cornersR = cv2.findChessboardCorners(ChessImaR, chessboard_size,None)  # Define the number of chees corners we are looking for
        retL, cornersL = cv2.findChessboardCorners(ChessImaL, chessboard_size,None)  # Left side
        if (True == retR) & (True == retL):
            objpoints.append(objp)
            cv2.cornerSubPix(ChessImaR, cornersR, (11, 11), (-1, -1), criteria)
            cv2.cornerSubPix(ChessImaL, cornersL, (11, 11), (-1, -1), criteria)
            imgpointsR.append(cornersR)
            imgpointsL.append(cornersL)
    print('计算右相机内外参数...')


    retR, mtxR, distR, rvecsR, tvecsR = cv2.calibrateCamera(objpoints, imgpointsR, ChessImaR.shape[::-1], None, None)
    hR, wR = ChessImaR.shape[:2]

    OmtxR, roiR = cv2.getOptimalNewCameraMatrix(mtxR, distR,
                                                (wR, hR), 1, (wR, hR))

    camera_calibration_config['retR'] = retR
    camera_calibration_config['mtxR'] = mtxR
    camera_calibration_config['distR'] = distR
    camera_calibration_config['rvecsR'] = rvecsR
    camera_calibration_config['tvecsR'] = tvecsR
    camera_calibration_config['hR'] = hR
    camera_calibration_config['wR'] = wR
    camera_calibration_config['OmtxR'] = OmtxR
    camera_calibration_config['OroiR'] = roiR
    camera_calibration_config['imageR_shape'] = ChessImaR.shape[::-1]

    #   Left Side
    print('计算左相机内外参数...')
    retL, mtxL, distL, rvecsL, tvecsL = cv2.calibrateCamera(objpoints, imgpointsL, ChessImaL.shape[::-1], None, None)
    hL, wL = ChessImaL.shape[:2]
    OmtxL, roiL = cv2.getOptimalNewCameraMatrix(mtxL, distL, (wL, hL), 1, (wL, hL))

    camera_calibration_config
    camera_calibration_config['mtxL']=mtxL
    camera_calibration_config['OmtxL']=OmtxL
    camera_calibration_config['distL']=distL
    camera_calibration_config['rvecsL']=rvecsL
    camera_calibration_config['tvecsL']=tvecsL
    camera_calibration_config['retL']=retL
    camera_calibration_config['hL']=hL
    camera_calibration_config['wL']=wL
    camera_calibration_config['OroiL']=roiL
    camera_calibration_config['imageL_shape'] = ChessImaL.shape[::-1]


    print('相机参数标定完毕，结果如下：')

    # ***** Calibrate the Cameras for Stereo *****
    retS, MLS, dLS, MRS, dRS, R, T, E, F = cv2.stereoCalibrate(objpoints, imgpointsL, imgpointsR, mtxL, distL, mtxR, distR,
                                                               ChessImaR.shape[::-1], criteria=criteria_stereo,
                                                               flags=cv2.CALIB_FIX_INTRINSIC)

    camera_calibration_config['MLS']=MLS
    camera_calibration_config['dLS']=dLS
    camera_calibration_config['MRS']=MRS
    camera_calibration_config['dRS']=dRS
    camera_calibration_config['R']=R
    camera_calibration_config['T']=T
    camera_calibration_config['E']=E
    camera_calibration_config['F']=F
    camera_calibration_config['retS']=retS


    # StereoRectify function
    rectify_scale = 0  # if 0 image croped, if 1 image nor croped
    RL, RR, PL, PR, Q, roiL, roiR = cv2.stereoRectify(MLS, dLS, MRS, dRS,
                                                      ChessImaR.shape[::-1], R, T,
                                                      rectify_scale,
                                                      (0, 0))  # last paramater is alpha, if 0= croped, if 1= not croped

    camera_calibration_config['RL']=RL
    camera_calibration_config['RR']=RR
    camera_calibration_config['PL']=PL
    camera_calibration_config['PR']=PR
    camera_calibration_config['Q']=Q
    camera_calibration_config['Rect_roiL']=roiL
    camera_calibration_config['Rect_roiR']=roiR

    # initUndistortRectifyMap function
    Left_Stereo_Map = cv2.initUndistortRectifyMap(MLS, dLS, RL, PL, ChessImaL.shape[::-1], cv2.CV_16SC2)  # cv2.CV_16SC2 this format enables us the programme to work faster
    Right_Stereo_Map = cv2.initUndistortRectifyMap(MRS, dRS, RR, PR, ChessImaR.shape[::-1], cv2.CV_16SC2)

    camera_calibration_config['Left_Stereo_Map'] = Left_Stereo_Map
    camera_calibration_config['Right_Stereo_Map'] = Right_Stereo_Map

    # 输出结果
    print("左相机内参:\n", mtxL)
    print("左相机畸变系数:\n", distL)
    print("右相机内参:\n", mtxR)
    print("右相机畸变系数:\n", distR)
    print("旋转矩阵 R:\n", R)
    print("平移向量 T:\n", T)
    print("本质矩阵 E:\n", E)
    print("基本矩阵 F:\n", F)
    print("---标定完成---")


    with open(camera_config_path, "wb") as f:
        pickle.dump(camera_calibration_config, f)

    camera_config_folder = '标定参数'
    params_backup_path = os.path.join(camera_config_folder, 'camera_params.pkl')
    with open(params_backup_path, "wb") as f:
        pickle.dump(camera_calibration_config, f)

    print(f'相机参数文件已经保存至：{camera_config_path}')
    print(f'相机参数文件已经保存至副本：{params_backup_path}')

    return params_backup_path

# if __name__ == '__main__':
#
#     # root_path = r'D:\Projects\Local_projects\demo_project\stereo_vision\chessboard_imgs_3'  # 棋盘格文件夹
#     root_path = '棋盘格图像'  # 棋盘格文件夹
#     camera_config_folder = '标定参数'
#     if not os.path.isdir(camera_config_folder):
#         os.makedirs(camera_config_folder)
#
#     left_img_path = os.path.join(root_path, '左')
#     right_img_path = os.path.join(root_path, '右')
#     camera_config_path = os.path.join(camera_config_folder, '相机参数.pkl')
#     kernel = np.ones((3, 3), np.uint8)
#     chessboard_size = (8, 6)
#     square_size=25
#
#     calibrate_camera(left_img_path=left_img_path, right_img_path=right_img_path, camera_config_path=camera_config_path)


