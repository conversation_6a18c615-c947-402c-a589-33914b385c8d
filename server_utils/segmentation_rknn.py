import os
import glob as gb
import cv2
import numpy as np
import platform
from rknnlite.api import RKNNLite
from PIL import Image
import json
import copy
import time
import typing as t
import sys

#from .utils import *
from .postprocess import segmentation_postprocess, result_visualizing
from .segmentation import Segmentation

class SegmentationRKNN(Segmentation):

    def __init__(
        self,
        model_path: t.Optional[str] = None,
        **options
    ) -> None:
        model_path = model_path or SegmentationRKNN._get_filename_by_suffix(suffix=".rknn")
        #model_path = SegmentationRKNN._get_filename_by_suffix(suffix=".rknn")
        super(SegmentationRKNN, self).__init__(model_path=model_path, **options)
        print('SegmentationRKNN >>>> model path =', self._model_path)
        self.model = self._load_model(self._model_path)
        self.classes_dict = {'background': 0, 'tube': 1}

    def _load_model(self, model_path):
        rknn_lite = RKNNLite()
        # load RKNN model
        ret = rknn_lite.load_rknn(model_path)
        if ret != 0:
            print('Load RKNN model failed')
            sys.exit(ret)
        # init runtime environment
        ret = rknn_lite.init_runtime(core_mask=RKNNLite.NPU_CORE_0)
        if ret != 0:
            print('Init runtime environment failed')
            sys.exit(ret)

        return rknn_lite

    @staticmethod
    def _get_filename_by_suffix(suffix: str = ".rknn") -> t.Optional[str]:
        """ Automatically get the yolo model filename. """
        file_path: str = os.path.join(os.path.dirname(__file__), "models").replace("\n", "")
        if not os.path.exists(file_path):
            return None
        files: t.List[str] = gb.glob(file_path + "/*" + suffix)
        if len(files) == 0:
            return None
        return files[0]


    def preprocess(self, image):
        #img = Image.open(image)
        #if img.mode!='RGB':
        #    img = img.convert('RGB')
        origin = np.array(copy.deepcopy(image))
        img = np.array(image).astype('float32')#.transpose((2,0,1))

        print('img shape >>>>', img.shape)
        #img -= 127.5
        #img *= 0.007843
        img = img / 255.0
        img = img[np.newaxis,:]
        return origin, img

    def infer(self, img):
        # Inference
        outputs = self.model.inference(inputs=[img])
        return outputs

    def run_image_post_process(self, origin, outputs, cy):
        results = segmentation_postprocess(img=origin, pred_mask=outputs, cy=cy)
        return results

    def release_model(self):
        self._model.release()

    def main_point_localization(self,img, mode, cy):
        origin, img = self.preprocess(img)
        outputs = self.infer(img)
        angle, cx, cy, contour, img_vis = self.run_image_post_process(origin, outputs,cy=300)
        #if mode=='test':
        #    img_vis = result_visualizing(img_vis, tube_contour=contour, cx=cx)
        return angle, cx, cy, contour, img_vis


# if __name__ == '__main__':
#     rknn_model_path = '/home/<USER>/Desktop/app/fa/FabricUI/FabricUI/static/cuda_model_rknn/single/fast_yolo.rknn'
#     # rknn_model_path = '/home/<USER>/Desktop/app/fa/FabricUI/FabricUI/static/cuda_model_rknn/single/fast_yolo.rknn'
#     rknn_model_path = "/home/<USER>/Desktop/orangepi_server/dist/orangepi_server/_internal/models/model.rknn"
#     image = '/home/<USER>/Desktop/test.bmp'
#     pred_times = []
#     rknn_model = SegmentationRKNN(rknn_model_path)
#     for i in range(1):
#         start_time = time.time()
#         origin, img = rknn_model.preprocess(image)
#         # print('-- outputs:',img)
#         outputs = rknn_model.infer(img)
#         # print('-- outputs:',outputs)
#         _, cx, cy, _, img_vis = rknn_model.run_image_post_process(origin, outputs,cy=300)
#         duration = time.time() - start_time
#         #print('-- duration:',duration)
#         pred_times.append(duration)
#         print('cx', cx)
#         print('cy', cy)
#
#     print('---- average duration:',sum(pred_times) / len(pred_times))
#
#     #print('done')
#
#     rknn_model.release_model()
#

