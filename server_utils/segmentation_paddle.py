#!/usr/bin/python
# -*- coding: utf-8 -*-
import copy
import os
from datetime import datetime
import paddle
import glob as gb
import typing as t
from paddleseg.cvlibs import Confi<PERSON>,Seg<PERSON><PERSON><PERSON>, manager
from paddleseg.utils import get_sys_env, logger, utils
from paddleseg.core import predict, infer
from paddleseg.core.predict import preprocess

from paddleseg.transforms import Compose

from .util_functions import *
from .segmentation import Segmentation


class SegmentationPaddle(Segmentation):
# class SegmentationPaddle():

    def __init__(
        self,
        model_path: t.Optional[str] = None,
        config_path: t.Optional[str] = None,
        **options
    ) -> None:
        model_path = model_path or SegmentationPaddle._get_filename_by_suffix(suffix=".pdparams")
        super(SegmentationPaddle, self).__init__(model_path=model_path, **options)
        # self._model_path = model_path
        self._config_path = config_path or SegmentationPaddle._get_filename_by_suffix(suffix=".yml")
        self._device = options.get("device", "gpu")

        self._model, self._transforms = self._load_model()
        utils.load_entire_model(self._model, self._model_path)
        self._model.eval()

        self.classes_dict = {'background': 0, 'tube': 1}

    def _load_model(self):

        cfg = Config(self._config_path)
        builder = SegBuilder(cfg)

        # utils.show_env_info()
        # utils.show_cfg_info(cfg)

        utils.set_device(self._device)

        model = builder.model
        transforms = Compose(builder.val_transforms)

        return model, transforms

    def _predict(self, img):
        list_preds = []
        with paddle.no_grad():
            data = preprocess(img, self._transforms)

            pred, _ = infer.inference(
                self._model,
                data['img'],
                trans_info=data['trans_info'], )

            pred = paddle.squeeze(pred)
            pred = pred.numpy().astype('uint8')

            # end_time = time.time() - start_time
            list_preds.append(pred)

        return list_preds # , end_time

    @staticmethod
    def _get_filename_by_suffix(suffix: str = ".pdparams") -> t.Optional[str]:
        """ Automatically get the yolo model filename. """
        file_path: str = os.path.join(os.path.dirname(__file__), "models").replace("\n", "")
        if not os.path.exists(file_path):
            return None
        files: t.List[str] = gb.glob(file_path + "/*" + suffix)
        if len(files) == 0:
            return None
        return files[0]

    def _result_visualizing(self, img, img_path, **options):
        result = copy.deepcopy(img)
        angle = options.get('angle', None)
        cx = options.get('cx', None)
        cy = options.get('cy', None)
        tube_contour = options.get('tube_contour', None)
        weld_contour = options.get('weld_contour', None)

        if tube_contour is not None:
            result = cv2.drawContours(img, tube_contour, -1, (0, 0, 255), 2)
        if weld_contour is not None:
            result = cv2.drawContours(result, [weld_contour], -1, (0, 0, 255), 2)
        if cx is not None:
            result = cv2.line(result, (0, cy), (img.shape[1] - 1, cy), (0, 255, 0), 2)
        if cy is not None:
            result = cv2.line(result, (cx, 0), (cx, img.shape[0] - 1), (0, 255, 0), 2)
        if angle is not None:
            result = cv2.putText(result, f'angle:{angle}', (20, 50), cv2.FONT_HERSHEY_PLAIN, 2, (0, 0, 255), 2)

        # cv2.imencode('.png', result)[1].tofile(img_path)
        return result

    def main_point_localization(
        self,
        img: np.ndarray,
        **options
    ) -> t.Tuple[t.Optional[float], t.Optional[int], t.Optional[int], t.Optional[t.List[t.List[int]]]]:
        """ Main method for the main point localization. It returns the tube's rotation angle, x-pixel offset,
            y-pixel offset, as well as the tube's polygon contour
        """
        cy = options.get('cy', None)
        cx = None

        angle = None
        # weld_contour = None
        tube_contour = None
        y_boundaries: t.List[int] = options.get('y_boundaries', None)  # y_boundaries=[ymin,ymax]
        mode = options.get('mode', 'prod')
        pred = self._predict(img=img)

        img_vis = copy.deepcopy(img)

        if len(pred) > 0:
            mask = pred[0]
            obj_value = self.classes_dict['tube']

            mask_obj = copy.deepcopy(mask)
            mask_obj[mask_obj != obj_value] = 0
            cnts, _ = cv2.findContours(mask_obj, cv2.RETR_TREE, cv2.CHAIN_APPROX_NONE)
            if len(cnts) > 0:
                cnt = list(sorted(cnts, key=lambda x: int(cv2.contourArea(x)), reverse=True))[0]
                tube_contour = copy.deepcopy(cnt)
                boundRect = cv2.minAreaRect(cnt)
                box = cv2.boxPoints(boundRect)
                box = np.intp(box)
                angle, pt1, pt2 = get_angle(box)
                if y_boundaries is not None:
                    ymin, ymax = y_boundaries
                    cnt = list(filter(lambda x: x[1] >= ymin and x[1] <= ymax, list(np.squeeze(cnt, axis=1))))

                if len(cnt) > 20:
                    cx, _ = center_by_moments(np.array(cnt))

            if mode == 'test':
                img_vis = self._result_visualizing(img_vis, f"{datetime.now().strftime('%Y-%m-%d_%H-%M-%S-%f')[:-3]}.png", cx=cx, cy=cy, tube_contour=tube_contour)
        return angle, cx, cy, tube_contour, img_vis




