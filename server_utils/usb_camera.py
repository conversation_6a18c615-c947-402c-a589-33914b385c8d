import logging

import cv2
import threading
import time
from queue import Queue

class USBCamera:
    _instance = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(USBCamera, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, max_queue_size=5):
        self.current_index = None
        self.cap = None
        self._initialized = True
        self.connect_camera()
        logging.warning("[USBCamera] 已连接摄像头")
        self.camera_is_configured = False
        if self.camera_is_connected() == 0:
            self.configure_camera()
            logging.warning("[USBCamera] 摄像头已配置")
        self.running = True
        self.frame_queue = Queue(maxsize=max_queue_size)
        self.thread = threading.Thread(target=self._update_frame, daemon=True)
        self.thread.start()
        logging.warning("[USBCamera] 摄像头线程已启动")

    def connect_camera(self):
        for index in range(5):  # 尝试 video0 ~ video4
            cap = cv2.VideoCapture(index)
            if cap.isOpened():
                self.cap = cap  # ✅ 把局部的 cap 存到实例变量 self.cap
                self.current_index = index
                logging.warning(f"[USBCamera] 成功连接到 /dev/video{index}")
                return
            cap.release()  # 释放当前 cap
        self.cap = None
        self.current_index = -1
        logging.error("[USBCamera] ❌ 无法连接任何可用摄像头（video0~video4）")

    def release_camera(self):
        self.running = False
        self.thread.join()
        self.cap.release()

    def configure_camera(self):
        #self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 2560)
        #self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)  # 或 960, 640
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        self.camera_is_configured = True

    def camera_is_connected(self):
        return 0 if self.cap and self.cap.isOpened() else -1

    def _update_frame(self):
        logging.warning("摄像头线程已启动")
        while self.running:
            if self.cap is None or not self.cap.isOpened():
                logging.warning("[USBCamera] 摄像头断开，正在尝试重连...")
                self.connect_camera()
                if self.cap is None:
                    time.sleep(2)
                    continue
                else:
                    self.configure_camera()
                    logging.info(f"[USBCamera] 重新连接成功：video{self.current_index}")
            ret, frame = self.cap.read()
            if ret:
                if self.frame_queue.full():
                    _ = self.frame_queue.get()
                self.frame_queue.put(frame)
            else:
                logging.warning("摄像头读取失败")
            time.sleep(0.01)

    def capture(self):
        if self.camera_is_connected() == -1:
            logging.warning("[USBCamera] 当前无摄像头连接")
            return None
        try:
            frame = self.frame_queue.get(timeout=1)  # 等待1秒内新帧
        except:
            return None

        #frameL = frame[0:720, 0:1280]
        #frameR = frame[0:720, 1280:2560]
        # 🔧 修复：交换左右图像分割位置
        frameL = frame[0:480, 640:1280]   # 左图：取原图的右半部分
        frameR = frame[0:480, 0:640]      # 右图：取原图的左半部分
        return [frameL, frameR]
