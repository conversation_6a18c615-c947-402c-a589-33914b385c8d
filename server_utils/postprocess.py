import cv2
import copy
import numpy as np
from datetime import datetime

classes_dict = {'background': 0, 'tube': 1}


def img_rotation(img: np.ndarray, angle: float):
    h, w = img.shape[:2]

    rot_mat = cv2.getRotationMatrix2D((w / 2, h / 2), angle, 1)
    res_im = copy.deepcopy(img)
    res_im = cv2.warpAffine(res_im, rot_mat, (w, h))
    return res_im


def get_angle(box_points: list):
    # 按照x值排列，取前两个点
    box_points = list(sorted(box_points, key=lambda x: x[0]))
    pt1, pt2 = box_points[:2]

    if pt1[1] > pt2[1]:  # 确保pt1在上 pt2在下
        pt1, pt2 = pt2, pt1

    # print('pt1={}, pt2={}'.format(pt1, pt2))
    x, y = pt1
    x1, y1 = pt2

    angle = np.rad2deg(np.arctan2(y1 - y, x1 - x))

    while angle >= 90:
        angle = angle - 90

    # bbox往右斜的情况下
    if x1 > x:
        angle = angle - 90

    return angle, pt1, pt2


def center_point(points):
    """center point of a rectangle"""
    x_coords = [x[0] for x in points]
    y_coords = [x[1] for x in points]

    c_x = int(sum(x_coords) / len(x_coords))
    c_y = int(sum(y_coords) / len(y_coords))
    return c_x, c_y


def center_by_moments(cont):
    cx, cy = 0,0
    M = cv2.moments(cont)
    if M['m00'] != 0:
        cx = int(M['m10'] / M['m00'])
        cy = int(M['m01'] / M['m00'])
        # cv2.drawContours(image, [i], -1, (0, 255, 0), 2)

    return cx, cy


def generate_interpolated_points(point1, point2, num_points):
    x1, y1 = point1
    x2, y2 = point2

    # Generate linearly spaced parameter values
    t = np.linspace(0, 1, num_points)

    # Linearly interpolate the x and y coordinates
    interpolated_x = x1 + (x2 - x1) * t
    interpolated_y = y1 + (y2 - y1) * t

    # Create a list of (x, y) tuples for the interpolated points
    interpolated_points = [[int(x), int(y)] for x, y in zip(interpolated_x, interpolated_y)]

    return interpolated_points


def center_mid(cont):
    cx, cy = 0, 0

    # cont = cv2.approxPolyDP(cont_orig, 0.7, True)
    # cont = np.squeeze(cont_orig, axis=1)
    # debug = list(sorted(cont, key=lambda x: (x[0], x[1]), reverse=True))
    right_x_min, _ = list(sorted(cont, key=lambda x: x[0]))[0]
    right_x_max, _ = list(sorted(cont, key=lambda x: x[0], reverse=True))[0]

    parts = 3
    x_dist = right_x_max - right_x_min
    x_range = int(x_dist / parts)

    mid = right_x_min + int((right_x_max-right_x_min)/2)

    # xmin_limit = right_x_min+x_range
    # xmax_limit = right_x_max-x_range
    cont = list(filter(lambda x: right_x_min+x_range<x[0]<right_x_max-x_range, cont))

    list_mid_ymin = list(sorted(cont, key=lambda x: (abs(mid - x[0]), x[1])))[:2]
    list_mid_ymax = list(sorted(cont, key=lambda x: (abs(mid - x[0]), x[1]), reverse=True))[:2]

    list_mid = list_mid_ymin + list_mid_ymax
    # list_rights = list(filter(lambda x: abs(mid - x[0]) <= 10, cont))
    list_mid_y = [x[1] for x in list_mid]
    list_mid_y = list(sorted(list_mid_y))

    cx = int(mid)
    cy = int((list_mid_y[0] + list_mid_y[-1]) / 2)
    return cx, cy

def result_visualizing(img, **options):
    result = copy.deepcopy(img)
    angle = options.get('angle', None)
    cx = options.get('cx', None)
    cy = options.get('cy', None)
    tube_contour = options.get('tube_contour', None)


    if tube_contour is not None:
        result = cv2.drawContours(img, tube_contour, -1, (0, 0, 255), 2)
    if cx is not None:
        result = cv2.line(result, (0, cy), (img.shape[1] - 1, cy), (0, 255, 0), 2)
    if cy is not None:
        result = cv2.line(result, (cx, 0), (cx, img.shape[0] - 1), (0, 255, 0), 2)
    if angle is not None:
        result = cv2.putText(result, f'angle:{angle}', (20, 50), cv2.FONT_HERSHEY_PLAIN, 2, (0, 0, 255), 2)

    return result


def segmentation_postprocess(img, pred_mask, **options):
    y_boundaries = options.get('y_boundaries', None)
    mode = options.get('mode', 'prod')
    cy = options.get('cy', None)

    cx = None
    angle = None
    # weld_contour = None
    tube_contour = None

    img_vis = copy.deepcopy(img)

    if len(pred_mask) > 0:
        mask = pred_mask[0]
        obj_value = classes_dict['tube']

        if len(mask.shape)==3:
            mask_obj = np.squeeze(copy.deepcopy(mask)).astype(np.uint8)
        else:
            mask_obj = copy.deepcopy(mask)

        mask_obj[mask_obj != obj_value] = 0
        cnts, _ = cv2.findContours(mask_obj, cv2.RETR_TREE, cv2.CHAIN_APPROX_NONE)
        if len(cnts) > 0:
            cnt = list(sorted(cnts, key=lambda x: int(cv2.contourArea(x)), reverse=True))[0]
            tube_contour = copy.deepcopy(cnt)
            boundRect = cv2.minAreaRect(cnt)
            box = cv2.boxPoints(boundRect)
            box = np.intp(box)
            angle, pt1, pt2 = get_angle(box)
            if y_boundaries is not None:
                ymin, ymax = y_boundaries
                cnt = list(filter(lambda x: x[1] >= ymin and x[1] <= ymax, list(np.squeeze(cnt, axis=1))))

            if len(cnt) > 20:
                cx, _ = center_by_moments(np.array(cnt))

        # if mode == 'test':
        #     save_path = f"{datetime.now().strftime('%Y-%m-%d_%H-%M-%S-%f')[:-3]}.png"
        # img_vis = result_visualizing(img_vis, save_path, cx=cx, cy=cy, tube_contour=tube_contour)
        img_vis = result_visualizing(img_vis, cx=cx, cy=cy, tube_contour=tube_contour)
            # print('图像保存至：',save_path)

    return angle, cx, cy, tube_contour, img_vis
