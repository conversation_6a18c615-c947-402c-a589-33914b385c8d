#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PLC通信调试测试脚本
用于测试和调试PLC通信问题
"""

import socket
import struct
import time
import threading

def pack_plc_command(pos, heartbeat, manual_flag, auto_flag, **kwargs):
    """
    打包PLC命令数据
    """
    pos_bytes = struct.pack("!6f", *pos)
    
    control_list = [
        heartbeat,                                    # 25 心跳
        manual_flag,                                  # 26 手动
        auto_flag,                                    # 27 自动
        kwargs.get("preparing_request", 0),           # 28 准备请求
        255 if kwargs.get("plc_ready_clamp", False) else 0,  # 29 PLC准备入夹
        255 if kwargs.get("not_in_position", False) else 0,  # 30 未到位
        255 if kwargs.get("in_position", False) else 0,      # 31 到位
        255 if kwargs.get("start_judgment", False) else 0,   # 32 开始判定
        0,  # 33
        0,  # 34
        0,  # 35
        255 if kwargs.get("result_received", False) else 0,  # 36 结果确认
        0,  # 37
        0,  # 38
        0,  # 39
        0,  # 40
        0,  # 41
        0   # 42
    ]
    
    # 填充为48字节
    full_bytes = pos_bytes + bytes(control_list) + bytes(48 - 24 - len(control_list))
    return full_bytes

def parse_orangepi_response(data):
    """
    解析香橙派响应数据
    """
    if len(data) != 48:
        raise ValueError("数据长度错误")
    
    pos_floats = struct.unpack("!6f", data[:24])
    controls = struct.unpack("!18B", data[24:42])
    
    return {
        "position": pos_floats,
        "heartbeat": controls[0],
        "manual": controls[1] == 255,
        "auto": controls[2] == 255,
        "preparing": controls[3],      # 28位状态码
        "ready": controls[4],          # 29位状态码
        "clamp_ack": controls[5],      # 30位状态码
        "judging": controls[6],        # 31位状态码
        "judgment_complete": controls[7],  # 32位状态码
        "result_ng": controls[15] == 255,  # 40位
        "result_ok": controls[16] == 255   # 41位
    }

def test_connection():
    """
    测试基本连接
    """
    print("=== 测试PLC连接 ===")
    
    # 尝试不同的IP地址
    test_ips = ["localhost", "127.0.0.1", "*************", "0.0.0.0"]
    
    for ip in test_ips:
        try:
            print(f"尝试连接到 {ip}:2000...")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2.0)
            sock.connect((ip, 2000))
            print(f"✅ 成功连接到 {ip}:2000")
            
            # 发送测试数据
            pos = [100.0, 200.0, 300.0, 0.0, 0.0, 0.0]
            cmd = pack_plc_command(pos, 1, 0, 255)  # 自动模式
            
            print(f"发送数据长度: {len(cmd)} 字节")
            print(f"发送数据内容: {cmd.hex()}")
            
            sock.sendall(cmd)
            
            # 接收响应
            response = sock.recv(48)
            print(f"接收数据长度: {len(response)} 字节")
            print(f"接收数据内容: {response.hex()}")
            
            if len(response) == 48:
                parsed = parse_orangepi_response(response)
                print(f"解析结果: {parsed}")
            
            sock.close()
            return ip  # 返回成功的IP
            
        except Exception as e:
            print(f"❌ 连接 {ip}:2000 失败: {e}")
            try:
                sock.close()
            except:
                pass
    
    return None

def test_auto_mode_simple():
    """
    测试简单的自动模式流程
    """
    print("\n=== 测试自动模式流程 ===")
    
    # 先测试连接
    working_ip = test_connection()
    if not working_ip:
        print("❌ 无法连接到PLC服务器")
        return
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect((working_ip, 2000))
        print(f"✅ 连接成功: {working_ip}:2000")
        
        pos = [100.0, 200.0, 300.0, 0.0, 0.0, 0.0]
        heartbeat = 1
        
        # 步骤1: 启动自动模式
        print("\n--- 步骤1: 启动自动模式 ---")
        cmd = pack_plc_command(pos, heartbeat, 0, 255)  # 自动模式
        sock.sendall(cmd)
        
        response = sock.recv(48)
        parsed = parse_orangepi_response(response)
        print(f"香橙派响应: {parsed}")
        
        time.sleep(1)
        heartbeat += 1
        
        # 步骤2: 等待准备完成
        print("\n--- 步骤2: 等待准备完成 ---")
        for i in range(3):
            cmd = pack_plc_command(pos, heartbeat, 0, 255)
            sock.sendall(cmd)
            
            response = sock.recv(48)
            parsed = parse_orangepi_response(response)
            print(f"第{i+1}次响应: 准备={parsed['preparing']}, 就绪={parsed['ready']}")
            
            if parsed['ready'] == 10:
                print("✅ 香橙派已准备就绪!")
                break
            
            time.sleep(0.5)
            heartbeat += 1
        
        # 步骤3: PLC准备入夹
        print("\n--- 步骤3: PLC准备入夹 ---")
        cmd = pack_plc_command(pos, heartbeat, 0, 255, plc_ready_clamp=True)
        sock.sendall(cmd)
        
        response = sock.recv(48)
        parsed = parse_orangepi_response(response)
        print(f"入夹确认响应: {parsed['clamp_ack']}")
        
        print("✅ 基本流程测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
    finally:
        try:
            sock.close()
        except:
            pass

def continuous_test():
    """
    持续测试，保持连接
    """
    print("\n=== 持续连接测试 ===")
    
    working_ip = test_connection()
    if not working_ip:
        print("❌ 无法连接到PLC服务器")
        return
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect((working_ip, 2000))
        print(f"✅ 持续连接成功: {working_ip}:2000")
        
        pos = [100.0, 200.0, 300.0, 0.0, 0.0, 0.0]
        heartbeat = 1
        
        print("开始持续发送数据（按Ctrl+C停止）...")
        
        while True:
            # 发送自动模式命令
            cmd = pack_plc_command(pos, heartbeat, 0, 255)
            sock.sendall(cmd)
            
            # 接收响应
            response = sock.recv(48)
            parsed = parse_orangepi_response(response)
            
            print(f"心跳={heartbeat}, 模式={'自动' if parsed['auto'] else '未知'}, "
                  f"准备={parsed['preparing']}, 就绪={parsed['ready']}, "
                  f"确认={parsed['clamp_ack']}")
            
            heartbeat = (heartbeat + 1) % 256
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户停止测试")
    except Exception as e:
        print(f"❌ 持续测试错误: {e}")
    finally:
        try:
            sock.close()
        except:
            pass

if __name__ == "__main__":
    print("PLC通信调试工具")
    print("================")
    
    while True:
        print("\n请选择测试模式:")
        print("1. 测试连接")
        print("2. 测试自动模式流程")
        print("3. 持续连接测试")
        print("0. 退出")
        
        choice = input("请输入选择 (0-3): ").strip()
        
        if choice == "1":
            test_connection()
        elif choice == "2":
            test_auto_mode_simple()
        elif choice == "3":
            continuous_test()
        elif choice == "0":
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")
