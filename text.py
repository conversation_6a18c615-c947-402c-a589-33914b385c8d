import sys
import socket
import struct
from PyQt5 import QtWidgets, QtCore


class PLCSimulator(QtWidgets.QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PLC 模拟器（持续连接）")
        self.setFixedSize(360, 500)

        layout = QtWidgets.QVBoxLayout()

        self.status = QtWidgets.QLabel("⚪ 未连接")
        layout.addWidget(self.status)

        self.response_label = QtWidgets.QLabel("等待接收响应...")
        self.response_label.setWordWrap(True)  # 自动换行
        layout.addWidget(self.response_label)

        # 控制按钮组
        self.buttons = {}
        control_names = {
            26: "手动模式",
            27: "自动模式",
            29: "PLC准备入夹",
            30: "PLC未到位",
            31: "PLC已到位",
            32: "开始判定",
            33: "夹紧",
            34: "已夹紧",
            35: "松夹",
            36: "已松夹",
            40: "结果确认"
        }

        for idx, name in control_names.items():
            btn = QtWidgets.QCheckBox(f"{idx} - {name}")
            layout.addWidget(btn)
            self.buttons[idx] = btn

        self.setLayout(layout)

        # 尝试连接 socket
        self.sock = None
        self.connect_socket("*************", 2000)

        # 定时器每 500ms 自动发送
        self.timer = QtCore.QTimer()
        self.timer.timeout.connect(self.send_data)
        self.timer.start(500)

    def connect_socket(self, ip, port):
        try:
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.settimeout(3.0)
            self.sock.connect((ip, port))
            self.status.setText(f"🟢 已连接 {ip}:{port}")
        except Exception as e:
            self.status.setText(f"🔴 连接失败: {e}")
            self.sock = None

    def send_data(self):
        if not self.sock:
            return

        # 前24字节填充6个 float32 坐标（全 0）
        pos = [0.0] * 6
        pos_bytes = struct.pack("!6f", *pos)

        ctrl_bytes = bytearray(48 - 24)

        for idx, checkbox in self.buttons.items():
            if checkbox.isChecked() and 25 <= idx <= 42:
                ctrl_bytes[idx - 25] = 255

        data = pos_bytes + bytes(ctrl_bytes)

        try:
            self.sock.sendall(data)
            response = self.sock.recv(48)
            hex_str = response.hex()
            formatted = ' '.join([hex_str[i:i+2] for i in range(0, len(hex_str), 2)])

            print(f"✅ 已发送 | 响应长度: {len(response)} 字节")
            self.status.setText("🟢 通讯中")
            self.response_label.setText(f"响应数据:\n{formatted}")

        except Exception as e:
            print("❌ 通讯失败:", e)
            self.status.setText("🔴 通讯异常，断开")
            self.timer.stop()
            try:
                self.sock.close()
            except:
                pass
            self.sock = None


if __name__ == "__main__":
    app = QtWidgets.QApplication(sys.argv)
    window = PLCSimulator()
    window.show()
    sys.exit(app.exec_())
