#!/usr/bin/python
# -*- coding: utf-8 -*-

import re
import time
import copy
import typing as t
import numpy as np
from loguru import logger

from .abstract import AbstractRobot, RobotMonitor
from hd_robotics.third_party.kuka_rc import KukaRC
from hd_robotics.core.utility import Utility, executable, check_emergency


class KukaRobot(AbstractRobot):
    """ Jaka robot wrapper. """
    SLIN_POS = 1
    SLIN_AXIS = 2
    PTP_AXIS = 3
    PTP_POS = 4
    FORWARD = 5
    INVERSE = 6
    IO_TRUE = 7
    IO_FALSE = 8

    def __init__(
        self,
        ip_addr: str,
        rapid_rate: float = 0.5,
        max_speed: float = 100.0,
        **options
    ) -> None:
        super(KukaRobot, self).__init__(ip_addr=ip_addr, rapid_rate=rapid_rate, max_speed=max_speed, **options)
        self._robot: t.Any = KukaRC(ip_addr)
        self._monitor: RobotMonitor = RobotMonitor(robot=self)
        self._max_speed = max(1.0, min(max_speed, 100.0))
        self._states_list: t.List[str] = [
            "$ACT_TOOL",  # 可以用来判断使用的是哪个工具坐标
            "$MODE_OP",  # 可以用来判断"#T1"，"#T2"，"#AUT"和"#EXT"
            "$PRO_STATE",  # 可以用来判断程序是否正常执行，但无法判断急停还是关闭使能
            "$PRO_IP",  # 可以用来判断执行的程序是不是HD_RUNNER，但似乎不是最佳选择，具体原因你测试一下就知道了
            "$PRO_MODE",  # 可以判断是正常模式还是单步模式
            "$TOOL"  # KUKA机器人外部TCP坐标系
        ]
        self._base_info: t.Dict[str, t.Any] = {
            "tcp_pos": {"x": 0.0, "y": 0.0, "z": 0.0, "rx": 0.0, "ry": 0.0, "rz": 0.0 },
            "joint_pos": {"j1": 0.0, "j2": 0.0, "j3": 0.0, "j4": 0.0, "j5": 0.0, "j6": 0.0},
            "external_pos": {"e1": 0.0},
            "gripper_on": self._gripper_on,
            "states": self._robot_states
        }

    @property
    def info(self, **options) -> t.Optional[t.Dict[str, t.Any]]:
        """ Returns the robot utility's information dictionary. """
        robot_info: t.Dict[str, t.Any] = copy.deepcopy(self._base_info)
        code_tcp, tcp_position = self.get_tcp_position()
        code_joint, joint_position = self.get_joint_position()
        code_external, external_position = self.get_external_position()
        code_states, self._robot_states = self.get_robot_status()
        if code_tcp != Utility.SUCCESS or code_joint != Utility.SUCCESS or \
           code_states != Utility.SUCCESS or code_external != Utility.SUCCESS:
            self._auto_running = False
            return robot_info
        elif len(tcp_position) != 6 or len(joint_position) != 6:
            self._auto_running = False
            return robot_info
        if self._robot_states["$MODE_OP"] == "#AUT" and self._robot_states["$PRO_STATE"] == "#P_ACTIVE":
            self._auto_running = True
        else:
            self.abort()
            self._auto_running = False
        robot_info["tcp_pos"]["x"] = tcp_position[0]
        robot_info["tcp_pos"]["y"] = tcp_position[1]
        robot_info["tcp_pos"]["z"] = tcp_position[2]
        robot_info["tcp_pos"]["rx"] = tcp_position[3]
        robot_info["tcp_pos"]["ry"] = tcp_position[4]
        robot_info["tcp_pos"]["rz"] = tcp_position[5]
        robot_info["joint_pos"]["j1"] = joint_position[0]
        robot_info["joint_pos"]["j2"] = joint_position[1]
        robot_info["joint_pos"]["j3"] = joint_position[2]
        robot_info["joint_pos"]["j4"] = joint_position[3]
        robot_info["joint_pos"]["j5"] = joint_position[4]
        robot_info["joint_pos"]["j6"] = joint_position[5]
        robot_info["external_pos"]["e1"] = external_position[0]
        robot_info["gripper_on"] = self._gripper_on
        robot_info["states"] = self._robot_states
        return robot_info

    @property
    def is_connected(self) -> bool:
        """ Returns whether the utility is connected or not. """
        if self._robot is None:
            return False
        return self._robot.is_connected()

    def connect(self, **options) -> bool:
        """ Connect to the robot, it returns whether the connection is successful. """
        res: bool = self._robot.connect()
        return res

    def disconnect(self, **options) -> bool:
        """ Disconnect to the robot, it returns whether the connection has been terminated. """
        return self._robot.disconnect()

    def destroy(self, **options) -> bool:
        """ Destroy to the robot, it releases the resources and returns whether the process has been succeeded. """
        self._monitor.exit()
        return self.disconnect(**options)

    def suspend(self, **options) -> bool:
        """ Suspend the robot's current activity. """
        return self.abort(**options)

    def abort(self, **options) -> bool:
        """ Abort the utility's current activity. """
        # if not self._auto_running:
        #     return True
        code: int = self.motion_abort()[0]
        if code == Utility.SUCCESS:
            return True
        else:
            return False

    def set_validation_mode(self, **options) -> bool:
        """ Set the validation mode, in which case the utility is applied for validation. """
        if self._mode == "validation":
            return False
        self._mode = "validation"
        return True

    def set_execution_mode(self, **options) -> bool:
        """ Set the execution mode, in which case the utility is ready to run. """
        if self._mode == "execution":
            return False
        self._mode = "execution"
        return True

    @executable
    def get_robot_status(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot's current status. """
        state_list = options.get("state_list", self._states_list)
        state_dict = dict()
        try:
            for item in state_list:
                rec = self._robot.read(item)
                state_dict[item] = rec
        except Exception:
            logger.exception("KukaRobot: State Acquire error")
            return AbstractRobot.ERROR, state_dict
        return AbstractRobot.SUCCESS, state_dict

    @executable
    def set_robot_status(self, write_dict, **options) -> t.Tuple[int, t.Any]:
        """ Set the robot's current status. """
        state_dict = copy.deepcopy(write_dict)
        try:
            for var, val in state_dict.items():
                rec = self._robot.write(var, val)
        except Exception:
            logger.exception("KukaRobot: State Acquire error")
            return AbstractRobot.ERROR, state_dict
        return AbstractRobot.SUCCESS, state_dict

    @executable
    def get_rapid_rate(self, **options) -> t.Tuple[int, float]:
        """ Get the robot's current rapid rate. """
        return AbstractRobot.SUCCESS, self._rapid_rate

    @executable
    def set_rapid_rate(self, rapid_rate: float, **options) -> t.Tuple[int, t.Any]:
        """ Set the robot's current rapid rate. """
        self._rapid_rate = max(0.01, min(rapid_rate, 1.0)) * 100
        self._robot.write("$OV_PRO", self._rapid_rate)
        return AbstractRobot.SUCCESS, None

    @executable
    def set_max_speed(self, max_speed: float, **options) -> t.Tuple[int, t.Any]:
        """ Set the robot's current max speed. """
        self._max_speed = max_speed
        return AbstractRobot.SUCCESS, None

    @executable
    def get_max_speed(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot's current max speed. """
        return AbstractRobot.SUCCESS, self._max_speed

    @executable
    def set_digital_output(
        self,
        io_type: int,
        index: int,
        value: t.Any,
        **options
    ) -> t.Tuple[int, t.Any]:
        """ Set the robot digital output, it returns whether the process is succeeded. """
        return Utility.SUCCESS, True

    @executable
    def linear_move(
        self,
        end_pos: t.List[float],
        move_mode: int = 0,
        is_block: bool = True,
        speed: int = 10,
        radian: bool = True,
        accuracy: int = 0,
        **options
    ) -> t.Tuple[int, t.Any]:
        """ Robot TCP linear move. """
        if not self._auto_running or check_emergency():
            return -1, None
        if not self._smooth_transition:
            accuracy = 0
        if radian:
            end_pos = self.rad_pos_2_deg_pos(end_pos)
        if move_mode == 1:
            err, cur_pos_rad = self.get_tcp_position()
            cur_pos_matrix = self.pos_to_matrix(cur_pos_rad)

            delta_rot_pos_deg = [0., 0., 0.] + end_pos[3:6]
            delta_rot_pos_rad = self.deg_pos_2_rad_pos(delta_rot_pos_deg)
            delta_rot_matrix = self.pos_to_matrix(delta_rot_pos_rad)
            end_rot_matrix = np.dot(delta_rot_matrix, cur_pos_matrix)
            end_rot_pos = self.matrix_to_pos(end_rot_matrix)
            end_rot_pos_deg = self.rad_pos_2_deg_pos(end_rot_pos)

            for i in range(3):
                end_pos[i] += cur_pos_rad[i]
            end_pos[3:6] = end_rot_pos_deg[3:6]
        pos_str = "{{X {:.6f}, Y {:.6f}, Z {:.6f}, A {:.6f}, B {:.6f}, C {:.6f}}}".format(*end_pos)
        try:
            speed = self._transfer_robot_speed(speed)
            self._robot.write("HDMOVE_SPEED", speed)
            self._robot.write("HDMOVE_ACCURACY", accuracy)
            self._robot.write("HDPOS", pos_str)
            self._robot.write("HDMOVE_MODE", KukaRobot.SLIN_POS)
            while True:
                time.sleep(0.1)
                rec = self._robot.read("HDMOVE_MODE")
                if int(rec) == 0:
                    break
                if not self._auto_running:
                    return -1, None
        except Exception:
            logger.exception("KukaRobot: Linear move error")
            return -1, None
        return 0, None

    @executable
    def linear_move_from_angle(
        self,
        joint_pos: t.List[float],
        move_mode: int = 0,
        is_block: bool = True,
        speed: int = 10,
        radian: bool = True,
        accuracy: int = 0,
        **options
    ) -> t.Tuple[int, t.Any]:
        """ Robot TCP linear move from joint angle positions. """
        if not self._auto_running or check_emergency():
            return -1, None
        if not self._smooth_transition:
            accuracy = 0
        if radian:
            joint_pos = self.rad_joint_2_deg_joint(joint_pos)
        joint_str = "{{A1 {:.6f}, A2 {:.6f}, A3 {:.6f}, A4 {:.6f}, A5 {:.6f}, A6 {:.6f}}}".format(*joint_pos)
        try:
            speed = self._transfer_robot_speed(speed)
            self._robot.write("HDMOVE_SPEED", speed)
            self._robot.write("HDMOVE_ACCURACY", accuracy)
            self._robot.write("HDAXIS", joint_str)
            self._robot.write("HDMOVE_MODE", KukaRobot.SLIN_AXIS)
            while True:
                time.sleep(0.1)
                rec = self._robot.read("HDMOVE_MODE")
                if int(rec) == 0:
                    break
                if not self._auto_running:
                    return -1, None
        except Exception:
            logger.exception("KukaRobot: Linear move from angle error")
            return -1, None
        return 0, None

    @executable
    def joint_move(
        self,
        joint_pos: t.List[float],
        move_mode: int = 0,
        is_block: bool = True,
        speed: int = 10,
        radian: bool = True,
        accuracy: int = 0,
        **options
    ) -> t.Tuple[int, t.Any]:
        """ Robot joint move function. """
        if not self._auto_running or check_emergency():
            return -1, None
        if not self._smooth_transition:
            accuracy = 0
        if radian:
            joint_pos = self.rad_joint_2_deg_joint(joint_pos)
        if move_mode == 1:
            err, cur_joint_rad = self.get_joint_position()
            cur_joint_deg = self.rad_joint_2_deg_joint(cur_joint_rad)
            for i in range(6):
                joint_pos[i] += cur_joint_deg[i]
        joint_str = "{{A1 {:.6f}, A2 {:.6f}, A3 {:.6f}, A4 {:.6f}, A5 {:.6f}, A6 {:.6f}}}".format(*joint_pos)
        try:
            speed = self._transfer_robot_speed(speed)
            self._robot.write("HDMOVE_SPEED", speed)
            self._robot.write("HDMOVE_ACCURACY", accuracy)
            self._robot.write("HDAXIS", joint_str)
            self._robot.write("HDMOVE_MODE", KukaRobot.PTP_AXIS)
            while True:
                time.sleep(0.1)
                rec = self._robot.read("HDMOVE_MODE")
                if int(rec) == 0:
                    break
                if not self._auto_running:
                    return -1, None
        except Exception:
            logger.exception("KukaRobot: Joint move error")
            return -1, None
        return 0, None

    @executable
    def joint_move_from_angle(
        self,
        joint_pos: t.List[float],
        move_mode: int = 0,
        is_block: bool = True,
        speed: int = 10,
        radian: bool = True,
        accuracy: int = 0,
        **options
    ) -> t.Tuple[int, t.Any]:
        """ Robot joint move from angle position. """
        if not self._auto_running or check_emergency():
            return -1, None
        if not self._smooth_transition:
            accuracy = 0
        if radian:
            joint_pos = self.rad_joint_2_deg_joint(joint_pos)
        joint_str = "{{A1 {:.6f}, A2 {:.6f}, A3 {:.6f}, A4 {:.6f}, A5 {:.6f}, A6 {:.6f}}}".format(*joint_pos)
        try:
            speed = self._transfer_robot_speed(speed)
            self._robot.write("HDMOVE_SPEED", speed)
            self._robot.write("HDMOVE_ACCURACY", accuracy)
            self._robot.write("HDAXIS", joint_str)
            self._robot.write("HDMOVE_MODE", KukaRobot.PTP_AXIS)
            while True:
                time.sleep(0.1)
                rec = self._robot.read("HDMOVE_MODE")
                if int(rec) == 0:
                    break
                if not self._auto_running:
                    return -1, None
        except Exception:
            logger.exception("KukaRobot: Joint move from angle error")
            return -1, None
        return 0, None

    @executable
    def joint_move_from_tcp(
        self,
        tcp_pos: t.List[float],
        move_mode: int = 0,
        is_block: bool = True,
        speed: int = 10,
        accuracy: int = 0,
        **options
    ) -> t.Tuple[int, t.Any]:
        """ Robot joint move from angle position. """
        if not self._auto_running or check_emergency():
            return -1, None
        if not self._smooth_transition:
            accuracy = 0
        pos_deg = self.rad_pos_2_deg_pos(tcp_pos)
        pos_str = "{{X {:.6f}, Y {:.6f}, Z {:.6f}, A {:.6f}, B {:.6f}, C {:.6f}}}".format(*pos_deg)
        try:
            speed = self._transfer_robot_speed(speed)
            self._robot.write("HDMOVE_SPEED", speed)
            self._robot.write("HDMOVE_ACCURACY", accuracy)
            self._robot.write("HDPOS", pos_str)
            self._robot.write("HDMOVE_MODE", KukaRobot.PTP_POS)
            while True:
                time.sleep(0.1)
                rec = self._robot.read("HDMOVE_MODE")
                if int(rec) == 0:
                    break
                if not self._auto_running:
                    return -1, None
        except Exception:
            logger.exception("KukaRobot: Joint move from tcp error")
            return -1, None
        return 0, None

    @executable
    def gripper_on(self, **options) -> t.Tuple[int, t.Any]:
        """ Robot gripper on function. """
        if not self._auto_running or check_emergency():
            return -1, None
        digital_outputs: t.List[t.List[t.Any]] = options.get("digital_outputs", [[1, True], [2, False]])
        try:
            for output in digital_outputs:
                code, _ = self._write_digital_output(port=output[0], state=output[1])
                if code != AbstractRobot.SUCCESS:
                    return code, None
            self._gripper_on = True
            time.sleep(1)
        except Exception:
            logger.exception("KukaRobot: Gripper on error")
            return -1, None
        return 0, None

    @executable
    def gripper_off(self, **options) -> t.Tuple[int, t.Any]:
        """ Robot gripper off function. """
        if not self._auto_running or check_emergency():
            return -1, None
        digital_outputs: t.List[t.List[t.Any]] = options.get("digital_outputs", [[1, False], [2, True]])
        try:
            for output in digital_outputs:
                code, _ = self._write_digital_output(port=output[0], state=output[1])
                if code != AbstractRobot.SUCCESS:
                    return code, None
            self._gripper_on = False
            time.sleep(1)
        except Exception:
            logger.exception("KukaRobot: Gripper off error")
            return -1, None
        return 0, None

    def _write_digital_output(self, port, state) -> t.Tuple[int, t.Any]:
        """ Robot joint move from angle position. """
        try:
            self._robot.write("HDIO_PORT", port)
            if state:
                self._robot.write("HDMOVE_MODE", KukaRobot.IO_TRUE)
            else:
                self._robot.write("HDMOVE_MODE", KukaRobot.IO_FALSE)
            while True:
                time.sleep(0.1)
                rec = self._robot.read("HDMOVE_MODE")
                if int(rec) == 0:
                    break
                if not self._auto_running:
                    return -1, None
        except Exception:
            logger.exception("KukaRobot: IO input error")
            return -1, None
        return 0, None

    def get_digital_input(self, port: int, **options) -> t.Tuple[int, t.Any]:
        """ get the Robot digital input port. """
        send_str = "$IN[{}]".format(port)
        try:
            rec = self._robot.read(send_str)
            if rec == "TRUE":
                return 0, True
            elif rec == "FALSE":
                return 0, False
            else:
                return -1, None
        except Exception:
            logger.exception("KukaRobot: IO input error")
            return -1, None

    @executable
    def motion_abort(self, **options) -> t.Tuple[int, t.Any]:
        """ Abort the current robt motion. """
        try:
            self._robot.write("HDABORT", True)
            time.sleep(0.1)
            self._robot.write("HDABORT", False)
        except Exception:
            logger.exception("KukaRobot: Motion abort error")
            return -1, None
        return 0, None

    @executable
    def get_tcp_position(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot's TCP position. """
        # pattern = r"[XYZABC]\s*(-?\d+(?:\.\d+)?)"
        pattern = r"[XYZABC]\s*([-+]?\d+(?:\.\d+)?(?:[eE][-+]?\d+)?)"
        try:
            rec = self._robot.read("$POS_ACT")
            tcp_str = re.findall(pattern, rec)
            xyz_position = [float(match) for match in tcp_str[:3]]
            abc_position = [float(match)*np.pi/180 for match in tcp_str[3:6]]
            tcp_position = xyz_position + abc_position
            return 0, tcp_position
        except Exception:
            logger.exception("KukaRobot: Get TCP position error")
            return -1, None

    @executable
    def get_joint_position(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot's joint position. """
        # pattern = r"A[1-6]\s*(-?\d+(?:\.\d+)?)"
        pattern = r"A[1-6]\s*([-+]?\d+(?:\.\d+)?(?:[eE][-+]?\d+)?)"
        try:
            rec = self._robot.read("$AXIS_ACT")
            joint_str = re.findall(pattern, rec)
            joint_position = [float(match)*np.pi/180 for match in joint_str[:6]]
            return 0, joint_position
        except Exception:
            logger.exception("KukaRobot: Get joint position error")
            return -1, None

    @executable
    def get_external_position(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot's external position. """
        # pattern = r"A[1-6]\s*(-?\d+(?:\.\d+)?)"
        pattern = r"E1\s*([-+]?\d+(?:\.\d+)?(?:[eE][-+]?\d+)?)"
        try:
            rec = self._robot.read("$AXIS_ACT")
            external_str = re.findall(pattern, rec)
            external_position = [float(match) for match in external_str[:1]]
            return 0, external_position
        except Exception:
            logger.exception("KukaRobot: Get external position error")
            return -1, None

    @executable
    def kine_forward(self, target_axis) -> t.Tuple[int, t.Any]:
        """ Calculate the joint pos by tcp pos. """
        pattern = r"[XYZABC]\s*(-?\d+(?:\.\d+)?)"
        joint_pos_deg = self.rad_joint_2_deg_joint(target_axis)
        joint_str = "{{A1 {:.6f}, A2 {:.6f}, A3 {:.6f}, A4 {:.6f}, A5 {:.6f}, A6 {:.6f}}}".format(*joint_pos_deg)
        try:
            self._robot.write("HDAXIS", joint_str)
            self._robot.write("HDMOVE_MODE", KukaRobot.FORWARD)
            while True:
                time.sleep(0.1)
                rec = self._robot.read("HDMOVE_MODE")
                if int(rec) == 0:
                    break
            rec = self._robot.read("HDPOS")
            tcp_str = re.findall(pattern, rec)
            xyz_position = [float(match) for match in tcp_str[:3]]
            abc_position = [float(match) * np.pi / 180 for match in tcp_str[3:6]]
            tcp_position = xyz_position + abc_position
        except Exception:
            logger.exception("KukaRobot: Kine forward error")
            return -1, None
        return 0, tcp_position

    @executable
    def kine_inverse(self, target_pos) -> t.Tuple[int, t.Any]:
        """ Calculate the joint pos by tcp pos. """
        pattern = r"A[1-6]\s*(-?\d+(?:\.\d+)?)"
        pos_deg = self.rad_pos_2_deg_pos(target_pos)
        pos_str = "{{X {:.6f}, Y {:.6f}, Z {:.6f}, A {:.6f}, B {:.6f}, C {:.6f}}}".format(*pos_deg)
        try:
            self._robot.write("HDPOS", pos_str)
            self._robot.write("HDMOVE_MODE", KukaRobot.INVERSE)
            while True:
                time.sleep(0.1)
                rec = self._robot.read("HDMOVE_MODE")
                if int(rec) == 0:
                    break
            rec = self._robot.read("HDAXIS")
            joint_str = re.findall(pattern, rec)
            joint_position = [float(match) * np.pi / 180 for match in joint_str[:6]]
        except Exception:
            logger.exception("KukaRobot: Kine inverse error")
            return -1, None
        return 0, joint_position

    def _transfer_robot_speed(self, speed: float) -> float:
        """ Transfer the robot speed given the execution or validation mode. """
        self._robot.write("$OV_PRO", self._rapid_rate)
        speed = max(1.0, min(speed, self._max_speed))
        return speed
