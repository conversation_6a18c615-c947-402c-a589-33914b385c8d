#!/usr/bin/python
# -*- coding: utf-8 -*-

import time
import typing as t

from loguru import logger

from .abstract import Abstract<PERSON>obot
from hd_robotics.core.utility import Utility, executable


class FakeRobot(AbstractRobot):
    """ Fake JAKA robot wrapper. """

    def __init__(
        self,
        ip_addr: str,
        rapid_rate: float = 0.5,
        max_speed: float = 10.0,
        **options
    ) -> None:
        super(FakeRobot, self).__init__(ip_addr=ip_addr, rapid_rate=rapid_rate, max_speed=max_speed, **options)
        self._is_motion_aborted: bool = False
        self._is_robot_connected: bool = False

        self._x: float = 0.0
        self._y: float = 0.0
        self._z: float = 0.0
        self._rx: float = 0.0
        self._ry: float = 0.0
        self._rz: float = 0.0

        self._a1: float = 0.0
        self._a2: float = 0.0
        self._a3: float = 0.0
        self._a4: float = 0.0
        self._a5: float = 0.0
        self._a6: float = 0.0

    @property
    def is_connected(self) -> bool:
        """ Returns whether the utility is connected or not. """
        return self._is_robot_connected

    def connect(self, **options) -> bool:
        """ Connect to the utility, it returns whether the connection is successful. """
        is_connected: bool = True
        self._is_robot_connected = is_connected
        return is_connected

    def disconnect(self, **options) -> bool:
        """ Disconnect to the utility, it returns whether the connection has been terminated. """
        is_disconnected: bool = True
        self._is_robot_connected = not is_disconnected
        return is_disconnected

    def destroy(self, **options) -> bool:
        """ Destroy to the robot, it releases the resources and returns whether the process has been succeeded. """
        return self.disconnect(**options)

    def suspend(self, **options) -> bool:
        """ Suspend the robot's current activity. """
        logger.info("FakeRobot: Suspend the current motion, it will call the abort function ...")
        return self.motion_abort()

    def abort(self, **options) -> bool:
        """ Abort the utility's current activity. """
        logger.info("FakeRobot: Abort the current motion.")
        code: int = self.motion_abort()[0]
        if code == Utility.SUCCESS:
            return True
        else:
            return False

    def set_validation_mode(self, **options) -> bool:
        """ Set the validation mode, in which case the utility is applied for validation. """
        logger.info("FakeRobot: Set into the validation mode.")
        self._mode = "validation"
        return True

    def set_execution_mode(self, **options) -> bool:
        """ Set the execution mode, in which case the utility is ready to run. """
        logger.info("FakeRobot: Set into the execution mode.")
        self._mode = "execution"
        return True

    @executable
    def power_on(self, **options) -> t.Tuple[int, t.Any]:
        """ Robot power on command. """
        logger.info("FakeRobot: Robot powered on.")
        return Utility.SUCCESS, None

    @executable
    def power_off(self, **options) -> t.Tuple[int, t.Any]:
        """ Robot power off command. """
        logger.info("FakeRobot: Robot powered off.")
        return Utility.SUCCESS, None

    @executable
    def shut_down(self, **options) -> t.Tuple[int, t.Any]:
        """ Shut down the robot controller. """
        logger.info("FakeRobot: Robot controller shut down.")
        return Utility.SUCCESS, None

    @executable
    def enable_robot(self, **options) -> t.Tuple[int, t.Any]:
        """ Enable robot command. """
        logger.info("FakeRobot: Robot enabled.")
        return Utility.SUCCESS, None

    @executable
    def disable_robot(self, **options) -> t.Tuple[int, t.Any]:
        """ disable robot command. """
        logger.info("FakeRobot: Robot disabled.")
        return Utility.SUCCESS, None

    @executable
    def login(self, **options) -> t.Tuple[int, t.Any]:
        """ Login the robot. """
        logger.info("FakeRobot: Login.")
        return Utility.SUCCESS, None

    @executable
    def logout(self, **options) -> t.Tuple[int, t.Any]:
        """ Logout the robot. """
        logger.info("FakeRobot: Logout.")
        return Utility.SUCCESS, None

    @executable
    def get_robot_status(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot's current status. """
        logger.info("FakeRobot: Get robot status.")
        return Utility.SUCCESS, [0, 0, 0, 0, 0, 0]

    @executable
    def get_rapid_rate(self, **options) -> t.Tuple[int, float]:
        """ Get the robot's current rapid rate. """
        return AbstractRobot.SUCCESS, self._rapid_rate

    @executable
    def set_rapid_rate(self, rapid_rate: float, **options) -> t.Tuple[int, t.Any]:
        """ Set the robot's current rapid rate. """
        self._rapid_rate = max(0.01, min(rapid_rate, 1.0)) * 100
        return AbstractRobot.SUCCESS, None

    @executable
    def set_max_speed(self, max_speed: float, **options) -> t.Tuple[int, t.Any]:
        """ Set the robot's current max speed. """
        self._max_speed = max_speed
        return AbstractRobot.SUCCESS, None

    @executable
    def get_max_speed(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot's current max speed. """
        return AbstractRobot.SUCCESS, self._max_speed

    @executable
    def get_digital_input(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot digital input. """
        logger.info("FakeRobot: Get digital input.")
        return Utility.SUCCESS, True

    @executable
    def get_digital_output(self, io_type: int, index: int, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot digital output. """
        logger.info("FakeRobot: Get digital output.")
        return Utility.SUCCESS, True

    @executable
    def set_digital_output(
        self,
        io_type: int,
        index: int,
        value: t.Any,
        **options
    ) -> t.Tuple[int, t.Any]:
        """ Set the robot digital output, it returns whether the process is succeeded. """
        return Utility.SUCCESS, True

    @executable
    def set_digital_output(
        self,
        io_type: int,
        index: int,
        value: t.Any,
        **options
    ) -> t.Tuple[int, t.Any]:
        """ Set the robot digital output, it returns whether the process is succeeded. """
        logger.info("FakeRobot: Digital output set.")
        return Utility.SUCCESS, None

    @executable
    def linear_move(
        self,
        end_pos: t.List[float],
        move_mode: int = 0,
        is_block: bool = True,
        speed: float = 10.0,
        **options
    ) -> t.Tuple[int, t.Any]:
        """ Robot TCP linear move. """
        speed = self._transfer_robot_speed(speed)
        logger.info(f"Robot Linear Move: The current speed is: {speed}")
        if not options.get("is_modify", False):
            interval: float = 0.1
            self._update_tcp_position(position=end_pos, move_mode=move_mode)
            for i in range(1):
                # hd_print("FakeRobot: Linear moving: {}".format(end_pos))
                time.sleep(interval)
                if self._is_motion_aborted:
                    break
            if self._is_motion_aborted:
                self._is_motion_aborted = False
        else:
            self._update_tcp_position(position=end_pos, move_mode=move_mode)
        return Utility.SUCCESS, None

    @executable
    def linear_move_from_angle(
        self,
        joint_pos: t.List[float],
        move_mode: int = 0,
        is_block: bool = True,
        speed: float = 10.0,
        **options
    ) -> t.Tuple[int, t.Any]:
        speed = self._transfer_robot_speed(speed)
        """ Robot TCP linear move from joint positions. """
        logger.info(f"Robot Linear Move From Angle: The current speed is: {speed}")
        if not options.get("is_modify", False):
            interval: float = 0.1
            self._update_joint_position(position=joint_pos, move_mode=move_mode)
            for i in range(1):
                # hd_print("FakeRobot: Linear moving from joint: {}".format(joint_pos))
                time.sleep(interval)
                if self._is_motion_aborted:
                    break
            if self._is_motion_aborted:
                self._is_motion_aborted = False
        else:
            self._update_joint_position(position=joint_pos, move_mode=move_mode)
        return Utility.SUCCESS, None

    @executable
    def joint_move(
        self,
        joint_pos: t.List[float],
        move_mode: int = 0,
        is_block: bool = True,
        speed: float = 10.0,
        **options
    ) -> t.Tuple[int, t.Any]:
        speed = self._transfer_robot_speed(speed)
        self._update_joint_position(position=joint_pos, move_mode=move_mode)
        """ Robot joint move from angle. """
        logger.info(f"Robot Joint Move: The current speed is: {speed}")
        # time.sleep(100)
        return Utility.SUCCESS, None

    @executable
    def joint_move_from_tcp(
        self,
        tcp_pos: t.List[float],
        move_mode: int = 0,
        is_block: bool = True,
        speed: int = 10,
        **options
    ) -> t.Tuple[int, t.Any]:
        """ Robot joint move from tcp position. """
        speed = self._transfer_robot_speed(speed)
        logger.info(f"Robot Joint Move From TCP: The current speed is: {speed}")
        if not options.get("is_modify", False):
            interval: float = 0.1
            self._update_tcp_position(position=tcp_pos, move_mode=move_mode)
            for i in range(1):
                # hd_print("FakeRobot: Joint moving from tcp position: {}".format(tcp_pos))
                time.sleep(interval)
                if self._is_motion_aborted:
                    break
            if self._is_motion_aborted:
                self._is_motion_aborted = False
        else:
            self._update_tcp_position(position=tcp_pos, move_mode=move_mode)
        return Utility.SUCCESS, None

    @executable
    def joint_move_from_angle(
        self,
        joint_pos: t.List[float],
        move_mode: int = 0,
        is_block: bool = True,
        speed: float = 10.0,
        **options
    ) -> t.Tuple[int, t.Any]:
        """ Robot joint move from angle. """
        speed = self._transfer_robot_speed(speed)
        logger.info(f"Robot Joint Move From Angle: The current speed is:{speed}")
        if not options.get("is_modify", False):
            interval: float = 0.1
            self._update_joint_position(position=joint_pos, move_mode=move_mode)
            for i in range(1):
                # hd_print("FakeRobot: Joint moving: {}".format(joint_pos))
                time.sleep(interval)
                if self._is_motion_aborted:
                    break
            if self._is_motion_aborted:
                self._is_motion_aborted = False
        else:
            self._update_joint_position(position=joint_pos, move_mode=move_mode)
        return Utility.SUCCESS, None

    @executable
    def motion_abort(self, **options) -> t.Tuple[int, t.Any]:
        """ Abort the current robt motion. """
        logger.info("FakeRobot: Motion abort.")
        self._is_motion_aborted = True
        return Utility.SUCCESS, None

    @executable
    def get_tcp_position(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot's TCP position. """
        tcp_pos: t.List[float] = [self._x, self._y, self._z, self._rx, self._ry, self._rz]
        return Utility.SUCCESS, tcp_pos

    @executable
    def get_joint_position(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot's joint position. """
        joint_pos: t.List[float] = [self._a1, self._a2, self._a3, self._a4, self._a5, self._a6]
        return Utility.SUCCESS, joint_pos

    @executable
    def get_external_position(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot's joint position. """
        external_pos: t.List[float] = [0.0]
        return Utility.SUCCESS, external_pos

    @executable
    def program_load(self, file_name: str, **options) -> t.Tuple[int, t.Any]:
        """ Load the robot program. """
        logger.info(f"FakeRobot: Program loaded {file_name}")
        return Utility.SUCCESS, None

    @executable
    def get_loaded_program(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot loaded program. """
        logger.info("FakeRobot: Get loaded program.")
        return Utility.SUCCESS, [0, 0, 0]

    @executable
    def program_run(self, **options) -> t.Tuple[int, t.Any]:
        """ Run the robot program. """
        logger.info("FakeRobot: Program run.")
        return Utility.SUCCESS, None

    @executable
    def program_abort(self, **options) -> t.Tuple[int, t.Any]:
        """ Abort the robot program. """
        logger.info("FakeRobot: Program aborted.")
        return Utility.SUCCESS, None

    def _update_tcp_position(self, position: t.List[float], move_mode: int) -> None:
        """ Update the current TCP position. """
        if len(position) < 6:
            return
        elif move_mode == 0:
            self._x, self._y, self._z, self._rx, self._ry, self._rz = position
        else:
            self._x += position[0]
            self._y += position[1]
            self._z += position[2]
            self._rx += position[3]
            self._ry += position[4]
            self._rz += position[5]

    def _update_joint_position(self, position: t.List[float], move_mode: int) -> None:
        """ Update the current TCP position. """
        if len(position) < 6:
            return
        elif move_mode == 0:
            self._a1, self._a2, self._a3, self._a4, self._a5, self._a6 = position
        else:
            self._a1 += position[0]
            self._a2 += position[1]
            self._a3 += position[2]
            self._a4 += position[3]
            self._a5 += position[4]
            self._a6 += position[5]

    @executable
    def kine_inverse(self, reference_joint, target_pos) -> t.Tuple[int, t.Any]:
        """ Calculate the joint pos by tcp pos. """
        return 0, reference_joint

    @executable
    def gripper_on(self, **options) -> t.Tuple[int, t.Any]:
        """ Robot gripper on function. """
        self._gripper_on = True
        return AbstractRobot.SUCCESS, None

    @executable
    def gripper_off(self, **options) -> t.Tuple[int, t.Any]:
        """ Robot gripper off function. """
        self._gripper_on = False
        return AbstractRobot.SUCCESS, None

    def _transfer_robot_speed(self, speed: float) -> float:
        """ Transfer the robot speed given the execution or validation mode. """
        speed = max(1.0, min(speed, self._max_speed))
        return speed
