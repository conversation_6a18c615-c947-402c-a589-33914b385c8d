#!/usr/bin/python
# -*- coding: utf-8 -*-

import time
import typing as t

from loguru import logger

from .abstract import Abs<PERSON><PERSON><PERSON><PERSON>, RobotMonitor
from hd_robotics.third_party import jkrc
from hd_robotics.core.utility import Utility, executable


class JakaRobot(AbstractRobot):
    """ Jaka robot wrapper. """

    def __init__(
        self,
        ip_addr: str,
        rapid_rate: float = 0.5,
        max_speed: float = 100.0,
        **options
    ) -> None:
        super(JakaRobot, self).__init__(ip_addr=ip_addr, rapid_rate=rapid_rate, max_speed=max_speed, **options)
        self._robot: t.Any = jkrc.RC(ip_addr)
        self._monitor: RobotMonitor = RobotMonitor(robot=self)
        self._is_robot_connected: bool = False

    @property
    def is_connected(self) -> bool:
        """ Returns whether the utility is connected or not. """
        return self._is_robot_connected

    def connect(self, **options) -> bool:
        """ Connect to the robot, it returns whether the connection is successful. """
        res: tuple = self._robot.login(**options)
        if res[0] == 0:
            res: tuple = self._robot.power_on()
        if res[0] == 0:
            res: tuple = self._robot.enable_robot()
        if res[0] == 0:
            is_connected: bool = True
        else:
            is_connected: bool = False

        if is_connected:  # Update the current execution adn validation rapid rate.
            self.set_rapid_rate(self._rapid_rate)
        self._is_robot_connected = is_connected
        return is_connected

    def disconnect(self, **options) -> bool:
        """ Disconnect to the robot, it returns whether the connection has been terminated. """
        res: tuple = self._robot.logout(**options)
        if res[0] == 0:
            is_disconnected: bool = True
        else:
            is_disconnected: bool = False
        self._is_robot_connected = not is_disconnected
        return is_disconnected

    def destroy(self, **options) -> bool:
        """ Destroy to the robot, it releases the resources and returns whether the process has been succeeded. """
        self._monitor.exit()
        return self.disconnect(**options)

    def suspend(self, **options) -> bool:
        """ Suspend the robot's current activity. """
        return self.abort(**options)

    def abort(self, **options) -> bool:
        """ Abort the utility's current activity. """
        code: int = self.motion_abort()[0]
        if code == Utility.SUCCESS:
            return True
        else:
            return False

    def set_validation_mode(self, **options) -> bool:
        """ Set the validation mode, in which case the utility is applied for validation. """
        if self._mode == "validation":
            return False
        self._mode = "validation"
        return True

    def set_execution_mode(self, **options) -> bool:
        """ Set the execution mode, in which case the utility is ready to run. """
        if self._mode == "execution":
            return False
        self._mode = "execution"
        return True

    @executable
    def power_on(self, **options) -> t.Tuple[int, t.Any]:
        """ Robot power on command. """
        return self._robot.power_on()[0], None

    @executable
    def power_off(self, **options) -> t.Tuple[int, t.Any]:
        """ Robot power off command. """
        return self._robot.power_off()[0], None

    @executable
    def shut_down(self, **options) -> t.Tuple[int, t.Any]:
        """ Shut down the robot controller. """
        return self._robot.shut_down()[0], None

    @executable
    def enable_robot(self, **options) -> t.Tuple[int, t.Any]:
        """ Enable robot command. """
        return self._robot.enable_robot()[0], None

    @executable
    def disable_robot(self, **options) -> t.Tuple[int, t.Any]:
        """ Disable robot command. """
        return self._robot.disable_robot()[0], None

    @executable
    def login(self, **options) -> t.Tuple[int, t.Any]:
        """ Login the robot. """
        return self._robot.login()[0], None

    @executable
    def logout(self, **options) -> t.Tuple[int, t.Any]:
        """ Logout the robot. """
        return self._robot.logout()[0], None

    @executable
    def get_robot_status(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot's current status. """
        return self._robot.get_robot_status()

    @executable
    def get_digital_input(self, io_type: int, index: int, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot digital input. """
        return self._robot.get_digital_input(io_type, index)

    @executable
    def get_digital_output(self, io_type: int, index: int, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot digital output. """
        return self._robot.get_digital_output(io_type, index)

    @executable
    def set_digital_output(
        self,
        io_type: int,
        index: int,
        value: t.Any,
        **options
    ) -> t.Tuple[int, t.Any]:
        """ Set the robot digital output, it returns whether the process is succeeded. """
        return self._robot.set_digital_output(io_type, index, value)[0], None

    @executable
    def get_rapid_rate(self, **options) -> t.Tuple[int, float]:
        """ Get the robot's current rapid rate. """
        return AbstractRobot.SUCCESS, self._robot.get_rapidrate()

    @executable
    def set_rapid_rate(self, rapid_rate: float, **options) -> t.Tuple[int, t.Any]:
        """ Set the robot's current rapid rate. """
        if not 0.0 < rapid_rate <= 1.0:
            return AbstractRobot.ERROR, None
        self._rapid_rate = rapid_rate
        return self._robot.set_rapidrate(rapid_rate)[0], None

    @executable
    def set_max_speed(self, max_speed: float, **options) -> t.Tuple[int, t.Any]:
        """ Set the robot's current max speed. """
        self._max_speed = max_speed
        return AbstractRobot.SUCCESS, None

    @executable
    def get_max_speed(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot's current max speed. """
        return AbstractRobot.SUCCESS, self._max_speed

    @executable
    def linear_move(
        self,
        end_pos: t.List[float],
        move_mode: int,
        is_block: bool,
        speed: float,
        **options
    ) -> t.Tuple[int, t.Any]:
        """ Robot TCP linear move. """
        speed = max(1, int(min(speed, self._max_speed)))
        code: int = self._robot.linear_move(end_pos, move_mode, is_block, speed)[0]
        # if self.mode == "execution":
        #     time.sleep(0.5)
        i = 0
        while not self._robot.is_in_pos()[1]:
            i += 1
            if i >= 20:
                logger.info("JakaRobot: Linear moving: {}".format(end_pos))
                i = 0
            time.sleep(0.1)
        return code, None

    @executable
    def linear_move_from_angle(
        self,
        joint_pos: t.List[float],
        move_mode: int,
        is_block: bool,
        speed: float,
        **options
    ) -> t.Tuple[int, t.Any]:
        """ Robot TCP linear move from joint angle positions. """
        speed = max(1, int(min(speed, self._max_speed)))
        joint_pos_rad = self.deg_joint_2_rad_joint(joint_pos)
        err, end_pos = self._robot.kine_forward(joint_pos_rad)
        code: int = self._robot.linear_move(end_pos, move_mode, is_block, speed)[0]
        # time.sleep(0.5)
        i = 0
        while not self._robot.is_in_pos()[1]:
            i += 1
            if i >= 20:
                logger.info("JakaRobot: Linear moving from joint: {}".format(joint_pos))
                i = 0
            time.sleep(0.1)
        return code, None

    @executable
    def joint_move(
            self,
            joint_pos: t.List[float],
            move_mode: int,
            is_block: bool,
            speed: float,
            **options
    ) -> t.Tuple[int, t.Any]:
        """ Robot joint move function. """
        PI = 3.1415926
        deg_speed_max = self._max_speed  # Todo: There may be a converter function.
        joint_speed_max = deg_speed_max / 180 * PI
        if speed > joint_speed_max:
            speed = joint_speed_max
        code: int = self._robot.joint_move(joint_pos, move_mode, is_block, speed)[0]
        # time.sleep(0.5)
        i = 0
        while not self._robot.is_in_pos()[1]:
            i += 1
            if i >= 20:
                logger.info("JakaRobot: Linear moving: {}".format(joint_pos))
                i = 0
            time.sleep(0.1)
        return code, None

    @executable
    def joint_move_from_angle(
            self,
            joint_pos: t.List[float],
            move_mode: int,
            is_block: bool,
            speed: int,
            **options
    ) -> t.Tuple[int, t.Any]:
        """ Robot joint move from angle position. """
        PI = 3.1415926
        deg_speed_max = self._max_speed  # Todo: There may be a converter function.
        joint_speed_max = deg_speed_max / 180 * PI
        if speed > joint_speed_max:
            speed = joint_speed_max

        joint_pos_rad = self.deg_joint_2_rad_joint(joint_pos)
        result: t.Tuple = self._robot.joint_move(joint_pos_rad, move_mode, is_block, speed)
        # time.sleep(0.5)
        i = 0
        while not self._robot.is_in_pos()[1]:
            i += 1
            if i >= 20:
                logger.info("JakaRobot: Joint moving: {}".format(joint_pos))
                i = 0
            time.sleep(0.1)
        return result[0], None

    @executable
    def joint_move_from_tcp(
            self,
            tcp_pos: t.List[float],
            move_mode: int,
            is_block: bool,
            speed: int,
            **options
    ) -> t.Tuple[int, t.Any]:
        """ Robot joint move from angle position. """
        PI = 3.1415926
        deg_speed_max = self._max_speed  # Todo: There may be a converter function.
        joint_speed_max = deg_speed_max / 180 * PI
        if speed > joint_speed_max:
            speed = joint_speed_max

        current_joint = self.get_joint_position()[1]
        joint_pos_rad = self.kine_inverse(current_joint, tcp_pos)[1]
        result: t.Tuple = self._robot.joint_move(joint_pos_rad, move_mode, is_block, speed)
        # time.sleep(0.5)
        i = 0
        while not self._robot.is_in_pos()[1]:
            i += 1
            if i >= 20:
                logger.info("JakaRobot: Joint moving: {}".format(joint_pos_rad))
                i = 0
            time.sleep(0.1)
        return result[0], None

    @executable
    def motion_abort(self, **options) -> t.Tuple[int, t.Any]:
        """ Abort the current robt motion. """
        return self._robot.motion_abort()[0], None

    @executable
    def get_tcp_position(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot's TCP position. """
        return self._robot.get_tcp_position()

    @executable
    def get_joint_position(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot's joint position. """
        return self._robot.get_joint_position()

    @executable
    def program_load(self, file_name: str, **options) -> t.Tuple[int, t.Any]:
        """ Load the robot program. """
        return self._robot.program_load(file_name)[0], None

    @executable
    def get_loaded_program(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot loaded program. """
        return self._robot.get_loaded_program()

    @executable
    def program_run(self, **options) -> t.Tuple[int, t.Any]:
        """ Run the robot program. """
        return self._robot.program_run()[0], None

    @executable
    def program_abort(self, **options) -> t.Tuple[int, t.Any]:
        """ Abort the robot program. """
        return self._robot.program_abort()[0], None

    @executable
    def kine_inverse(self, reference_joint, target_pos) -> t.Tuple[int, t.Any]:
        """ Calculate the joint pos by tcp pos. """
        return self._robot.kine_inverse(reference_joint, target_pos)

    @executable
    def gripper_on(self, **options) -> t.Tuple[int, t.Any]:
        """ Robot gripper on function, this is a virtual function. """
        self._gripper_on = True
        return AbstractRobot.SUCCESS, None

    @executable
    def gripper_off(self, **options) -> t.Tuple[int, t.Any]:
        """ Robot gripper off function, this is a virtual function. """
        self._gripper_on = False
        return AbstractRobot.SUCCESS, None
