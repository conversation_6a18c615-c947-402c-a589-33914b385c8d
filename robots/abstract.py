#!/usr/bin/python
# -*- coding: utf-8 -*-

import time
import typing as t
import numpy as np
from threading import Thread
from abc import abstractmethod
from hd_robotics.core.utility import Utility, executable, check_emergency
from scipy.spatial.transform import Rotation


class AbstractRobot(Utility):
    """ Abstract robot controller object. """

    def __init__(
        self,
        ip_addr: str,
        rapid_rate: float = 0.5,
        max_speed: float = 10.0,
        **options
    ) -> None:
        self._ip_addr = ip_addr
        self._rapid_rate: float = max(0.01, min(rapid_rate, 1.0)) * 100
        self._max_speed = max(1.0, max_speed)
        self._gripper_on: bool = options.get("gripper_on", False)
        self._smooth_transition: bool = options.get("smooth_transition", False)
        self._robot_states: t.Dict[str, t.Any] = dict()
        self._auto_running: bool = True
        super(AbstractRobot, self).__init__(**options)

    @property
    def info(self, **options) -> t.Optional[t.Dict[str, t.Any]]:
        """ Returns the robot utility's information dictionary. """
        code_tcp, tcp_position = self.get_tcp_position()
        code_joint, joint_position = self.get_joint_position()
        if code_tcp != Utility.SUCCESS or code_joint != Utility.SUCCESS:
            return dict()
        elif len(tcp_position) != 6 or len(joint_position) != 6:
            return dict()
        robot_info: t.Dict[str, t.Any] = {
            "tcp_pos": {
                "x": tcp_position[0],
                "y": tcp_position[1],
                "z": tcp_position[2],
                "rx": tcp_position[3],
                "ry": tcp_position[4],
                "rz": tcp_position[5]
            },
            "joint_pos": {
                "j1": joint_position[0],
                "j2": joint_position[1],
                "j3": joint_position[2],
                "j4": joint_position[3],
                "j5": joint_position[4],
                "j6": joint_position[5]
            },
            "external_pos": {
                "e1": 0.0
            },
            "gripper_on": self._gripper_on,
        }
        return robot_info

    @executable
    def initialize(self, **options) -> t.Tuple[int, t.Any]:
        return AbstractRobot.SUCCESS, None

    @executable
    @abstractmethod
    def get_robot_status(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot's current status. """
        pass

    @abstractmethod
    def get_digital_input(self, **options) -> t.Tuple[int, t.Any]:
        """ get the Robot digital input port. """
        pass

    @executable
    @abstractmethod
    def set_digital_output(
        self,
        io_type: int,
        index: int,
        value: t.Any,
        **options
    ) -> t.Tuple[int, t.Any]:
        """ Set the robot digital output, it returns whether the process is succeeded. """
        pass

    @executable
    @abstractmethod
    def linear_move(
        self,
        end_pos: t.List[float],
        move_mode: int = 0,
        is_block: bool = True,
        speed: float = 10.0,
        **options
    ) -> t.Tuple[int, t.Any]:
        """ Robot TCP linear move. """
        pass

    @executable
    @abstractmethod
    def linear_move_from_angle(
        self,
        joint_pos: t.List[float],
        move_mode: int = 0,
        is_block: bool = True,
        speed: float = 10.0,
        **options
    ) -> t.Tuple[int, t.Any]:
        """ Robot TCP linear move from joint angle positions. """
        pass

    @executable
    @abstractmethod
    def joint_move(
        self,
        joint_pos: t.List[float],
        move_mode: int = 0,
        is_block: bool = True,
        speed: float = 10.0,
        **options
    ) -> t.Tuple[int, t.Any]:
        """ Robot TCP joint move function. """
        pass

    @executable
    @abstractmethod
    def joint_move_from_angle(
        self,
        joint_pos: t.List[float],
        move_mode: int = 0,
        is_block: bool = True,
        speed: float = 10.0,
        **options
    ) -> t.Tuple[int, t.Any]:
        """ Joint move from joint angle positions. """
        pass

    @executable
    @abstractmethod
    def joint_move_from_tcp(
        self,
        tcp_pos: t.List[float],
        move_mode: int,
        is_block: bool,
        speed: float = 10.0,
        **options
    ) -> t.Tuple[int, t.Any]:
        """ Robot joint move from angle position. """
        pass

    @executable
    @abstractmethod
    def motion_abort(self, **options) -> t.Tuple[int, t.Any]:
        """ Abort the current robt motion. """
        pass

    @executable
    @abstractmethod
    def get_tcp_position(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot's TCP position. """
        pass

    @executable
    @abstractmethod
    def get_joint_position(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot's joint position. """
        pass

    @executable
    @abstractmethod
    def get_external_position(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot's joint position. """
        pass

    @executable
    @abstractmethod
    def set_rapid_rate(self, rapid_rate: float, **options) -> t.Tuple[int, t.Any]:
        """ Set the robot's current rapid rate. """
        pass

    @executable
    @abstractmethod
    def get_rapid_rate(self, rapid_rate: float, **options) -> t.Tuple[int, float]:
        """ Get the robot's current rapid rate. """
        pass

    @executable
    @abstractmethod
    def set_max_speed(self, max_speed: float, **options) -> t.Tuple[int, t.Any]:
        """ Set the robot's current max speed. """
        pass

    @executable
    @abstractmethod
    def get_max_speed(self, **options) -> t.Tuple[int, t.Any]:
        """ Get the robot's current max speed. """
        pass

    @executable
    @abstractmethod
    def kine_inverse(self, reference_joint, target_pos) -> t.Tuple[int, t.Any]:
        """ Calculate the joint pos by tcp pos. """
        pass

    @executable
    @abstractmethod
    def gripper_on(self, **options) -> t.Tuple[int, t.Any]:
        """ Abstract method for robot gripper on function. """
        pass

    @executable
    @abstractmethod
    def gripper_off(self, **options) -> t.Tuple[int, t.Any]:
        """ Abstract method for robot gripper off function. """
        pass

    @staticmethod
    def deg_pos_2_rad_pos(degPos):
        # rx, ry, rz为角度
        x, y, z, rx, ry, rz = degPos

        rx = AbstractRobot.deg_to_rad(rx)
        ry = AbstractRobot.deg_to_rad(ry)
        rz = AbstractRobot.deg_to_rad(rz)
        return [x, y, z, rx, ry, rz]

    @staticmethod
    def deg_to_rad(degree):
        PI = 3.1415926
        return degree / 180 * PI

    @staticmethod
    def rad_to_deg(rad):
        PI = 3.1415926
        # rad = degree/180*PI
        degree = rad / PI * 180
        return degree

    @staticmethod
    def rad_pos_2_deg_pos(radPos):
        # rx, ry, rz为弧度
        x, y, z, rx, ry, rz = radPos

        rx = AbstractRobot.rad_to_deg(rx)
        ry = AbstractRobot.rad_to_deg(ry)
        rz = AbstractRobot.rad_to_deg(rz)
        return [x, y, z, rx, ry, rz]

    @staticmethod
    def deg_joint_2_rad_joint(degJoint):
        res = list(map(lambda x: AbstractRobot.deg_to_rad(x), degJoint))
        return res

    @staticmethod
    def rad_joint_2_deg_joint(radJoint):
        res = list(map(lambda x: AbstractRobot.rad_to_deg(x), radJoint))
        return res

    @staticmethod
    def pos_to_matrix(pos, **options):  # pos: x,y,y rx,ry,rz
        # rx, ry, rz为弧度
        x, y, z, rx, ry, rz = pos
        mode = options.get("mode", 'ZYX')
        matrix_3d = Rotation.from_euler(mode, [rx, ry, rz]).as_matrix()

        arr_xyz = np.array([x, y, z]).reshape(3, 1)
        matrix_4d = np.hstack((matrix_3d, arr_xyz))
        arr_aux = np.array([0, 0, 0, 1]).reshape(1, 4)
        matrix_4d_2 = np.vstack((matrix_4d, arr_aux))

        return matrix_4d_2

    @staticmethod
    def matrix_to_pos(matrix4d, **options):
        mode = options.get("mode", 'ZYX')
        x = matrix4d[0, 3]
        y = matrix4d[1, 3]
        z = matrix4d[2, 3]
        matrix3d = matrix4d[0:3, 0:3]

        rpy = Rotation.from_matrix(matrix3d).as_euler(mode)
        rx = rpy[0]
        ry = rpy[1]
        rz = rpy[2]

        res = [x, y, z, rx, ry, rz]
        return res


class RobotMonitor(Thread):
    """ Robot monitor in case for th emergency stop. """

    def __init__(self, robot: AbstractRobot, **options) -> None:
        super(RobotMonitor, self).__init__(**options)
        self._robot = robot
        self._is_exit: bool = False
        self._is_emergency: bool = check_emergency()
        self._interval: float = options.get("interval", 0.02)
        self._options = options
        self.start()

    def exit(self) -> None:
        """ Exit the looping monitoring """
        self._is_exit = True

    def run(self) -> None:
        """ Main monitoring loop. """
        while not self._is_exit:
            is_emergency: bool = check_emergency()
            if not self._is_emergency and is_emergency:
                self._robot.abort()
            self._is_emergency = is_emergency
            time.sleep(self._interval)

