from server_utils.usb_camera import USBCamera
import cv2
import os
import time
camera = USBCamera()
camera.start()

try:
    frame_count = 0
    while frame_count < 10:  # 读取前10帧
        frames = camera.capture()
        if frames:
            frameL, frameR = frames
            print(f"Captured frame {frame_count}")
            cv2.imwrite(f"left_{frame_count}.jpg", frameL)
            cv2.imwrite(f"right_{frame_count}.jpg", frameR)
            frame_count += 1
        time.sleep(0.1)
finally:
    camera.release_camera()
    camera.join()
