pyinstaller --add-data="config.json:_internal" --hidden-import=rknnlite --hidden-import=rknnlite.api.rknn_utils orangepi_server.py --hidden-import=rknnlite.api.rknn_model --hidden-import=rknnlite.utils --hidden-import=rknnlite.utils.yaml_parser --hidden-import=ruamel --hidden-import=ruamel.yaml  --hidden-import=PIL._tkinter_finder --add-data="/home/<USER>/anaconda3/envs/fastdeploy/lib/python3.9/site-packages/rknnlite:rknnlite" --hidden-import=tkinter --hidden-import=PIL --add-binary="/home/<USER>/anaconda3/envs/fastdeploy/lib/python3.9/site-packages/dist-packages/PIL/_imaging.cpython-39-aarch64-linux-gnu.so:."


