import json
import os
import time
import random
import socket
import struct
import threading
import typing as t
import numpy as np
from global_objects import localizer
from scipy.spatial.transform import Rotation
from server_utils.logger_config import logger as logging

plc_state_lock = threading.Lock()
current_mode = "unknown"  # "manual", "auto", "unknown"
manual_state = "idle"  # "idle", "preparing", "ready", "plc_ready", "clamping"
auto_state = "idle"  # "idle", "preparing", "ready", "plc_ready", "waiting_position", "judging", "result_sent", "waiting_ack"
heartbeat_counter = 0
last_judgment_result = None  # "OK" or "NG"
last_action = None  # 上一次动作（"grip" or "release"）
gripper_status = {
    "alarm": False,
    "is_gripped": False,
    "is_released": False,
}
last_return_coordinates = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
grippe_completed_state = {"clamped": False, "released": False}


def load_last_offset():
    try:
        with open("last_offset.json", "r") as f:
            data = json.load(f)
            return data.get("delta_pos_deg", [0.0, 0.0, 0.0, 0.0, 0.0, 0.0])
    except Exception as e:
        logging.warning(f"加载偏移量失败: {e}")
        return [0.0] * 6


def plc_socket_listener(host: str, port: int, localizer):
    """
    用于监听PLC端口（2000），接收PLC发来的控制数据
    """
    plc_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    plc_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    plc_socket.bind((host, port))
    plc_socket.listen(5)
    logging.info(f"[PLC] Listening on {host}:{port}")

    while True:
        conn, addr = plc_socket.accept()
        logging.info(f"[PLC] Connection from {addr}")
        threading.Thread(
            target=handle_plc_connection,
            args=(conn, addr),
            daemon=True
        ).start()


def recv_exact(sock, size):
    data = b''
    while len(data) < size:
        part = sock.recv(size - len(data))
        if not part:
            raise ConnectionError("连接关闭或无数据")
        data += part
    logging.info(f"[PLC] 收到 {len(data)} 字节数据: {data.hex()}")
    return data


def parse_plc_data(data: bytes) -> dict:
    """
    解析 PLC 发来的 48 字节控制数据包
    :param data: 原始字节流
    :return: dict 包含位置、模式、心跳、功能标志等
    """
    if len(data) != 48:
        raise ValueError("数据长度错误，应为48字节")
    pos_floats = struct.unpack("!6f", data[:24])
    controls = struct.unpack("!18B", data[24:42])  # 扩展到18个字节以覆盖所有控制位

    heartbeat = controls[0]  # 25位 心跳
    manual_flag = controls[1]  # 26位 手动模式
    auto_flag = controls[2]  # 27位 自动模式

    flags = {  # 映射成bool类型的字典和原始值
        "preparing_request": controls[3],  # 28位 准备请求（原始值）
        "plc_ready_clamp": controls[4] == 255,  # 29位 PLC准备入夹
        "not_in_position": controls[5] == 255,  # 30位 未到位信号
        "in_position": controls[6] == 255,  # 31位 到位信号
        "start_judgment": controls[7] == 255,  # 32位 开始判定
        "clamp_release": controls[8] == 255,  # 33位 松夹
        "clamp_grip": controls[9] == 255,  # 34位 夹紧
        "judgment_start": controls[10] == 255,  # 35位 判定开始
        "result_received": controls[11] == 255,  # 36位 结果接收
        # 扩展更多控制位
        "reserved_37": controls[12] == 255,  # 37位 预留
        "reserved_38": controls[13] == 255,  # 38位 预留
        "reserved_39": controls[14] == 255,  # 39位 预留
        "reserved_40": controls[15] == 255,  # 40位 预留
        "reserved_41": controls[16] == 255,  # 41位 预留
        "reserved_42": controls[17] == 255,  # 42位 预留
        # 原始控制值（用于特殊状态码）
        "raw_28": controls[3],  # 28位原始值
        "raw_29": controls[4],  # 29位原始值
        "raw_30": controls[5],  # 30位原始值
        "raw_31": controls[6],  # 31位原始值
        "raw_32": controls[7],  # 32位原始值
    }

    # 确定模式
    mode = "unknown"
    if manual_flag == 255 and auto_flag == 255:
        mode = "manual"  # 同时为255时优先手动模式
    elif manual_flag == 255:
        mode = "manual"
    elif auto_flag == 255:
        mode = "auto"
    else:
        mode = "unknown"

    return {
        "position": pos_floats,  # 六轴坐标
        "heartbeat": heartbeat,  # 心跳字节
        "mode": mode,  # 模式（manual/auto/unknown）
        "manual_flag": manual_flag,  # 原始手动标志
        "auto_flag": auto_flag,  # 原始自动标志
        "flags": flags,  # 所有控制信号布尔字典
        "raw_bytes": data  # 原始数据保留
    }


def check_gripper_alarm() -> bool:
    """
    检查夹爪是否有报警
    """
    # TODO: 替换为真实的报警检查逻辑
    return gripper_status["alarm"]


def send_grip_command() -> None:
    """
    向夹爪发送夹紧指令
    """
    # TODO: 替换为发送夹紧指令
    gripper_status["is_gripped"] = True
    gripper_status["is_released"] = False


def send_release_command() -> None:
    """
    向夹爪发送松开指令
    """
    # TODO: 替换为发送松开指令
    gripper_status["is_gripped"] = False
    gripper_status["is_released"] = True


def check_grip_done() -> bool:
    """
    检查夹紧是否完成
    """
    return gripper_status["is_gripped"]


def check_release_done() -> bool:
    """
    检查松开是否完成
    """
    return gripper_status["is_released"]


def handle_manual_mode_state_machine(parsed_data: dict) -> dict:
    global manual_state, last_action

    flags = parsed_data["flags"]
    response_flags = {
        "manual": True,
        "auto": False,
        "preparing": 0,
        "ready": 0,
        "clamp_ack": 0,
        "judging": 0,
        "judgment_complete": 0,
        "result_ng": False,
        "result_ok": False,
        "alarm_code": 0,
    }

    # Step 1: 检查报警
    if check_gripper_alarm():
        logging.warning("[Manual] 夹爪报警")
        response_flags["alarm_code"] = 255  # 43位对应索引42
        return response_flags

    # Step 2: 读取PLC指令
    want_release = flags.get("clamp_release")  # 33位松夹
    want_grip = flags.get("clamp_grip")  # 34位夹紧

    # Step 3: 响应夹紧指令
    if want_grip:
        if manual_state != "gripping":
            logging.info("[Manual] 收到夹紧请求，开始夹紧")
            send_grip_command()
            manual_state = "gripping"
            last_action = "grip"
        response_flags["clamp_ack"] = 255  # 33位 表示“正在夹紧”
        response_flags["working_code"] = 50
        if check_grip_done():
            logging.info("[Manual] 夹紧完成")
            response_flags["working_code"] = 60
            manual_state = "idle"
            response_flags["result_ok"] = True  # 39位 对应夹紧反馈
        return response_flags

    # Step 4: 响应松开指令
    if want_release:
        if manual_state != "releasing":
            logging.info("[Manual] 收到松开请求，开始松开")
            send_release_command()
            manual_state = "releasing"
            last_action = "release"
        response_flags["judging"] = 255  # 35位 表示“正在松开”
        response_flags["working_code"] = 70
        if check_release_done():
            logging.info("[Manual] 松开完成")
            manual_state = "idle"
            response_flags["judgment_complete"] = 255  # 38位 对应松开反馈
            response_flags["working_code"] = 80
        return response_flags

    # Step 5: 未收到新指令
    logging.debug("[Manual] 等待夹爪指令中...")
    return response_flags


def handle_auto_mode_state_machine(parsed_data: dict) -> dict:
    """
    处理自动模式的状态机逻辑
    自动模式流程：
    1. 收到26位=255 -> 28位=9（准备中）
    2. 准备完成 -> 29位=10（就绪）
    3. PLC 29位=255（准备入夹） -> 30位=20（确认入夹）
    4. PLC 30位=255（未到位）-> 等待
    5. PLC 31位=255（到位）-> 等待判定指令
    6. PLC 32位=255（开始判定）-> 31位=30（判定中）
    7. 判定完成 -> 32位=40（完成）+ 40/41位（结果）
    8. PLC 36位=255（结果确认）-> 重新开始循环
    """
    global auto_state, last_judgment_result

    flags = parsed_data["flags"]
    # 初始 flags 字典
    response_flags = {
        "manual": False,
        "auto": True,
        "preparing": 0,
        "ready": 0,
        "clamp_ack": 0,
        "judging": 0,
        "judgment_complete": 0,
        "result_ng": False,
        "result_ok": False,
        "working_code": 0
    }

    logging.info(f"[Auto] 当前状态: {auto_state}")

    if flags.get("preparing_request") == 255:
        logging.warning("中断指令，初始化！")
        grippe_completed_state["released"] = True
        response_flags["released"] = True
        response_flags["working_code"] = 80
        response_flags["releasing"] = False
        response_flags["clamp_release_feedback"] = True  # 38位
        auto_state = "preparing"

    elif auto_state == "idle":
        # 收到自动模式信号，开始准备
        logging.info("[Auto] 进入自动模式，开始准备")
        auto_state = "preparing"

    elif auto_state == "preparing":
        response_flags["preparing"] = True
        response_flags["working_code"] = 9
        # 模拟准备过程（这里可以添加实际的准备逻辑）
        logging.info("[Auto] 准备中...")
        # 假设准备完成，切换到就绪状态
        auto_state = "ready"

    elif auto_state == "ready":
        response_flags["ready"] = True  # 29位=10，准备就绪
        response_flags["working_code"] = 10
        # 等待PLC发送29位=255，表示PLC准备入夹
        if flags.get("plc_ready_clamp"):
            logging.info("[Auto] PLC准备入夹，确认开始入夹流程")
            auto_state = "plc_ready"

    elif auto_state == "plc_ready":
        # 等待PLC到位信号
        response_flags["clamp_ack"] = True  # 保持确认状态
        response_flags["working_code"] = 20

        if flags.get("not_in_position") and not flags.get("in_position"):
            logging.info("[Auto] PLC未到位，继续等待")
            auto_state = "waiting_position"
        elif flags.get("in_position") and not flags.get("not_in_position"):
            logging.info("[Auto] PLC已到位，等待判定指令")
            auto_state = "waiting_judgment"
        else:
            logging.info(f"{auto_state}")


    elif auto_state == "waiting_position":
        # 等待PLC到位
        # response_flags["clamp_ack"] = 20
        response_flags["working_code"] = 22
        auto_state = "judging"

    elif auto_state == "waiting_judgment":
        response_flags["working_code"] = 21
        if flags.get("clamp_grip"):  # 收到夹紧指令
            auto_state = "clamping"
            # response_flags["clamping"] = True  # 33位=50
            # send_clamp_grip_to_gripper()

    elif auto_state == "waiting_judgment1":
        logging.info("[Auto] 夹紧完成")
        response_flags["working_code"] = 60
        grippe_completed_state["clamped"] = True
        response_flags["clamped"] = True
        response_flags["clamping"] = False
        response_flags["clamp_grip_feedback"] = True
        if flags.get("clamp_release"):  # 收到松开指令
            auto_state = "releasing"
            # response_flags["releasing"] = True  # 35位=70
            # send_clamp_release_to_gripper()

    elif auto_state == "clamping":
        response_flags["working_code"] = 50
        if grippe_completed_state["clamped"]:
            response_flags["clamping"] = False
        else:
            response_flags["clamping"] = True
        # GPIO
        if True:
            auto_state = "waiting_judgment1"

    elif auto_state == "releasing":
        response_flags["working_code"] = 70
        if grippe_completed_state["released"]:
            response_flags["releasing"] = False
        else:
            response_flags["releasing"] = True
        if True:
            auto_state = "prepar"
            logging.info("[Auto] 松夹完成")

    elif auto_state == "prepar":
        grippe_completed_state["released"] = True
        response_flags["released"] = True
        response_flags["working_code"] = 80
        response_flags["releasing"] = False
        response_flags["clamp_release_feedback"] = True  # 38位
        if True:
            auto_state = "preparing"

    elif auto_state == "judging":
        # 执行判定逻辑
        response_flags["judging"] = True
        response_flags["working_code"] = 30
        logging.info("[Auto] 正在执行判定...")
        plc_coordinates = list(parsed_data["position"])

        # 调用视觉判定函数
        judgment_result, offset_coords = perform_vision_judgment()  # 传入实际的localizer
        last_judgment_result = judgment_result
        global last_return_coordinates

        if judgment_result == "OK":
            # OK：直接返回PLC坐标
            last_return_coordinates = plc_coordinates.copy()
            logging.info(f"[Auto] 判定OK - 返回PLC原始坐标: {last_return_coordinates}")
        else:
            # NG：返回PLC坐标+偏移量
            last_return_coordinates = [plc_coordinates[i] + offset_coords[i] for i in range(6)]
            logging.info(
                f"[Auto] 判定NG - 返回修正坐标: PLC{plc_coordinates} + 偏移{offset_coords} = {last_return_coordinates}")

        logging.info(f"[Auto] 判定完成，结果: {judgment_result}")
        auto_state = "result_sent"
        response_flags["judgment_complete"] = 40
        response_flags["working_code"] = 40
        response_flags["result_ok"] = (judgment_result == "OK")
        response_flags["result_ng"] = (judgment_result == "NG")

    elif auto_state == "result_sent":
        response_flags["judgment_complete"] = True
        response_flags["working_code"] = 40
        response_flags["result_ok"] = (last_judgment_result == "OK")
        response_flags["result_ng"] = (last_judgment_result == "NG")
        if flags.get("result_received"):
            logging.info("[Auto] PLC已确认收到结果，准备下一轮")
            auto_state = "preparing"  # 重新开始循环
            global last_return_coordinates, last_judgment_result
            last_return_coordinates = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
            last_judgment_result = None
            response_flags = {
                "manual": False,
                "auto": True,
                "preparing": 9,  # 重新开始准备
                "ready": 0,
                "clamp_ack": 0,
                "judging": 0,
                "judgment_complete": 0,
                "result_ng": False,
                "result_ok": False
            }

    return response_flags


def save_offset_to_json(T, save_path="configs/last_offset.json"):
    """
    将偏移量 T 保存到 JSON 文件
    :param T: list or np.ndarray, 3D 平移向量 [dx, dy, dz]
    :param save_path: 保存路径
    """
    try:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        # 构建字典结构
        data = {
            "delta_pos_mm": [float(val) for val in T]
        }
        with open(save_path, "w") as f:
            json.dump(data, f, indent=4)
        logging.info(f"偏移量已保存到 {save_path}: {data}")
    except Exception as e:
        logging.error(f"保存偏移量失败: {e}")


def handle_plc_connection(conn: socket.socket, addr):
    """
    处理PLC连接，实现手动和自动模式的状态机逻辑
    """
    global current_mode, manual_state, auto_state, heartbeat_counter, plc_state_lock
    logging.info(f"[PLC] Connected: {addr}")
    try:
        while True:
            # 接收PLC发来的48字节数据
            logging.info(f"[PLC] 等待接收数据...")
            data = recv_exact(conn, 48)
            logging.info(f"[PLC] 成功接收48字节数据")
            parsed_data = parse_plc_data(data)
            logging.info(f"[PLC] 解析数据: 模式={parsed_data['mode']}, 心跳={parsed_data['heartbeat']}")
            logging.info(f"[PLC] 控制标志: {parsed_data['flags']}")
            with plc_state_lock:
                current_mode = parsed_data["mode"]
                heartbeat_counter = (heartbeat_counter + 1) % 256
                # delta_pos_deg = load_last_offset()
                # status, current_pos, controls = _pos_correct(delta_pos_deg, data)
                # if status != 0:
                #     logging.warning(f"校正失败，断开连接")
                #     continue
                if auto_state == "result_sent" and 'last_return_coordinates' in globals() and last_return_coordinates:
                    # 返回视觉判定后的坐标
                    current_pos = last_return_coordinates.copy()
                    logging.info(f"[PLC] 返回视觉判定坐标: {current_pos}")
                    status = 0  # 成功状态
                else:
                    # 使用原有的位置修正逻辑
                    delta_pos_deg = load_last_offset()
                    status, current_pos, controls = _pos_correct(delta_pos_deg, data)
                    logging.info(f"[PLC] 使用位置修正坐标: {current_pos}")

                if status != 0:
                    logging.warning(f"校正失败，断开连接")
                    continue
                if current_mode == "manual":
                    response_flags = handle_manual_mode_state_machine(parsed_data)
                elif current_mode == "auto":
                    response_flags = handle_auto_mode_state_machine(parsed_data)
                else:
                    # 未知模式，重置状态
                    manual_state = "idle"
                    auto_state = "idle"
                    response_flags = {
                        "manual": False,
                        "auto": False,
                        "preparing": 0,
                        "ready": 0,
                        "clamp_ack": 0,
                        "judging": 0,
                        "judgment_complete": 0,
                        "result_ng": False,
                        "result_ok": False
                    }
                # 打包响应数据并发送
                response_data = pack_data_to_plc(current_pos, response_flags, heartbeat_counter)
                logging.info(f"[PLC] 发送响应数据: {len(response_data)} 字节")
                logging.info(f"[PLC] 响应标志: {response_flags}")
                conn.sendall(response_data)
                logging.info(f"[PLC] Mode: {current_mode}, Manual State: {manual_state}, Auto State: {auto_state}")
    except ConnectionError:
        logging.warning(f"[PLC] Connection closed by {addr}")
    except Exception as e:
        logging.error(f"[PLC] Exception: {e}")
    finally:
        conn.close()
        logging.info(f"[PLC] Connection closed: {addr}")


def pack_data_to_plc(pos: list[float], flags: dict, heartbeat: int = 0) -> bytes:
    """
    将香橙派要发给PLC的状态数据打包成48字节（前24位为坐标，25~42为控制状态位）
    :param pos: 6个float32，例如机械臂反馈位姿 [x, y, z, rx, ry, rz]
    :param flags: 字典形式的控制标志，每项为 True/False
    :param heartbeat: 心跳值（通常每帧+1 或恒定）
    :return: 48字节的bytes数据
    """
    if len(pos) != 6:
        raise ValueError("坐标长度必须为6")
    pos_bytes = struct.pack("!6f", *pos)
    # 按照第25~42位的控制位定义顺序排列，每个标志为0或255或特定状态码
    control_list = [
        heartbeat,  # 25 心跳
        255 if flags.get("manual", False) else 0,  # 26 手动模式反馈
        255 if flags.get("auto", False) else 0,  # 27 自动模式反馈
        255 if flags.get("preparing", False) else 0,# 28 正在准备 (值为9表示准备中)
        255 if flags.get("ready", False) else 0,  # 29 准备就绪 (值为10表示就绪)
        255 if flags.get("clamp_ack", False) else 0,  # 30 入夹确认 (值为20表示确认)
        255 if flags.get("judging", False) else 0, # 31 正在判定 (值为30表示判定中)
        255 if flags.get("judgment_complete", False) else 0,  # 32 判定完成 (值为40表示完成)
        255 if flags.get("clamping", False) else 0,  # 33 正在夹紧
        255 if flags.get("clamped", False) else 0,  # 34 已夹紧
        255 if flags.get("releasing", False) else 0,  # 35 正在松夹
        255 if flags.get("released", False) else 0,  # 36 已松夹
        flags.get("working_code", 0),  # ✅ 37 = 统一状态码
        # 255 if flags.get("working", False) else 0,         # 37 工作状态
        255 if flags.get("clamp_release_feedback", False) else 0,  # 38 松夹反馈
        255 if flags.get("clamp_grip_feedback", False) else 0,  # 39 夹紧反馈
        255 if flags.get("result_ng", False) else 0,  # 40 判定结果NG
        255 if flags.get("result_ok", False) else 0,  # 41 判定结果OK
        255 if flags.get("clamp_error", False) else 0  # 42 夹爪异常
    ]
    # 填充为 48 字节（前24坐标 + 18控制位 + 6备用）
    full_bytes = pos_bytes + bytes(control_list) + bytes(48 - 24 - len(control_list))
    return full_bytes


# def perform_vision_judgment():
#     """
#     执行视觉判定，返回判定结果和偏移坐标
#     判定标准：
#     - OK：平移矩阵第一个值接近61mm，后面两个接近0
#     - NG：不满足OK条件，返回修正后的坐标
#     """
#     try:
#         if localizer is None:
#             # 测试模式：模拟平移矩阵
#             import random
#
#             # 模拟平移矩阵的三个值
#             if random.random() > 0.3:  # 70%概率模拟OK情况
#                 translation_x = 61.0 + random.uniform(-2, 2)  # 接近61mm
#                 translation_y = random.uniform(-1, 1)  # 接近0
#                 translation_z = random.uniform(-1, 1)  # 接近0
#             else:  # 30%概率模拟NG情况
#                 translation_x = random.uniform(50, 70)  # 偏离61mm
#                 translation_y = random.uniform(-10, 10)  # 偏离0
#                 translation_z = random.uniform(-10, 10)  # 偏离0
#
#             # 模拟旋转偏移
#             rotation_rx = random.uniform(-2, 2)
#             rotation_ry = random.uniform(-2, 2)
#             rotation_rz = random.uniform(-2, 2)
#
#             offset_coords = [translation_x, translation_y, translation_z,
#                              rotation_rx, rotation_ry, rotation_rz]
#
#             logging.info(
#                 f"[Vision] 测试模式 - 平移矩阵: [{translation_x:.2f}, {translation_y:.2f}, {translation_z:.2f}]")
#
#         else:
#             # 实际视觉判定逻辑
#             logging.info("[Vision] 开始视觉判定...")
#
#             # 调用视觉定位主函数
#             vision_result = localizer.main_rectify_poses(save=True)
#
#             # 解析结果：[status_code, x, y, z, rx, ry, rz]
#             if len(vision_result) >= 7:
#                 status_code = vision_result[0]
#                 if status_code == 1:
#                     # 视觉定位成功，获取平移和旋转偏移
#                     translation_x, translation_y, translation_z = vision_result[1:4]
#                     rotation_rx, rotation_ry, rotation_rz = vision_result[4:7]
#
#                     offset_coords = [translation_x, translation_y, translation_z,
#                                      rotation_rx, rotation_ry, rotation_rz]
#
#                     logging.info(
#                         f"[Vision] 检测到平移矩阵: [{translation_x:.3f}, {translation_y:.3f}, {translation_z:.3f}]")
#                     logging.info(f"[Vision] 检测到旋转偏移: [{rotation_rx:.3f}, {rotation_ry:.3f}, {rotation_rz:.3f}]")
#
#                 else:
#                     # 视觉定位失败
#                     logging.warning(f"[Vision] 视觉定位失败，状态码: {status_code}")
#                     return "NG", [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
#             else:
#                 # 结果格式错误
#                 logging.error("[Vision] 视觉结果格式错误")
#                 return "NG", [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
#
#         # 判定逻辑：检查平移矩阵是否符合标准
#         target_x = 61.0  # 目标值61mm
#         tolerance_x = 3.0  # X方向容差
#         tolerance_yz = 2.0  # Y,Z方向容差
#
#         x_ok = abs(offset_coords[0] - target_x) < tolerance_x  # 第一个值接近61mm
#         y_ok = abs(offset_coords[1]) < tolerance_yz  # 第二个值接近0
#         z_ok = abs(offset_coords[2]) < tolerance_yz  # 第三个值接近0
#
#         if x_ok and y_ok and z_ok:
#             result = "OK"
#             logging.info(f"[Vision] 判定OK - 平移矩阵符合标准")
#             logging.info(f"[Vision] X偏差: {abs(offset_coords[0] - target_x):.3f}mm (容差: {tolerance_x}mm)")
#             logging.info(f"[Vision] Y偏差: {abs(offset_coords[1]):.3f}mm (容差: {tolerance_yz}mm)")
#             logging.info(f"[Vision] Z偏差: {abs(offset_coords[2]):.3f}mm (容差: {tolerance_yz}mm)")
#         else:
#             result = "NG"
#             logging.info(f"[Vision] 判定NG - 平移矩阵不符合标准")
#             logging.info(
#                 f"[Vision] X: {offset_coords[0]:.3f}mm (目标: {target_x}±{tolerance_x}mm) {'✓' if x_ok else '✗'}")
#             logging.info(f"[Vision] Y: {offset_coords[1]:.3f}mm (目标: 0±{tolerance_yz}mm) {'✓' if y_ok else '✗'}")
#             logging.info(f"[Vision] Z: {offset_coords[2]:.3f}mm (目标: 0±{tolerance_yz}mm) {'✓' if z_ok else '✗'}")
#
#         return result, offset_coords
#
#     except Exception as e:
#         logging.error(f"[Vision] 视觉判定异常: {e}")
#         return "NG", [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]

def perform_vision_judgment():
    """
    执行视觉判定，返回判定结果和坐标信息
    返回: (判定结果, 坐标偏移量)
    """
    try:
        if localizer is None:
            # 测试模式
            import random
            if random.random() > 0.3:
                result = "OK"
                offset_coords = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]  # OK时无偏移
            else:
                result = "NG"
                offset_coords = [2.5, -1.8, 0.5, 0.2, -0.1, 0.3]  # NG时有偏移
            logging.info(f"[Vision] 测试模式判定结果: {result}, 偏移: {offset_coords}")
            return result, offset_coords
        # 实际视觉判定逻辑
        logging.info("[Vision] 开始视觉判定...")
        vision_result = localizer.main_rectify_poses(save=True)
        if len(vision_result) >= 7:
            status_code = vision_result[0]
            if status_code == 1:
                offset_x, offset_y, offset_z, offset_rx, offset_ry, offset_rz = vision_result[1:7]
                offset_coords = [offset_x, offset_y, offset_z, offset_rx, offset_ry, offset_rz]
                # 判定容差
                position_tolerance = 5.0
                angle_tolerance = 5.0
                if (abs(offset_x) < position_tolerance and
                        abs(offset_y) < position_tolerance and
                        abs(offset_z) < position_tolerance and
                        abs(offset_rx) < angle_tolerance and
                        abs(offset_ry) < angle_tolerance and
                        abs(offset_rz) < angle_tolerance):
                    result = "OK"
                else:
                    result = "NG"
                logging.info(f"[Vision] 判定结果: {result}, 偏移: {offset_coords}")
                return result, offset_coords
        # 失败情况
        return "NG", [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
    except Exception as e:
        logging.error(f"[Vision] 视觉判定异常: {e}")
        return "NG", [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]

def _pos_correct(
        delta_pos_deg: t.List[float],
        data_bytes: bytes
) -> t.Tuple[int, t.List[float], t.List[int]]:
    """
    计算机器人从当前位置到焊接中心的目标位姿
    :param delta_pos_deg: 偏移角度（6维）
    :param data_bytes: 接收到的 48 字节数据（含坐标和控制字节）
    :return: 状态码、robot_to_target_pos（目标六轴坐标）、controls（原始控制字节）
    """
    try:
        coords = struct.unpack("!6f", data_bytes[:24])
        robot_to_clip_pos = list(coords)
        controls = list(struct.unpack("!8B", data_bytes[24:32]))

        logging.info(f"[Robot] Received coords: {robot_to_clip_pos}")
        logging.info(f"[Robot] Received control flags: {controls}")
    except Exception as e:
        logging.warning(f"坐标解析异常: {e}")
        return -1, [], []

    robot_to_clip_matrix = pos_to_matrix(robot_to_clip_pos)
    delta_pos = deg_pos_2_rad_pos(delta_pos_deg)
    delta_pos_matrix = pos_to_matrix(delta_pos)

    weld_to_robot5_pos_deg = [-450, -350, -200, 0, 0, 0]
    weld_to_robot_matrix = pos_to_matrix(deg_pos_2_rad_pos(weld_to_robot5_pos_deg))
    robot_to_weld_matrix = np.linalg.inv(weld_to_robot_matrix)

    pipe_to_clip_matrix = delta_pos_matrix @ weld_to_robot_matrix @ robot_to_clip_matrix
    robot_to_target_matrix = robot_to_weld_matrix @ pipe_to_clip_matrix

    robot_to_target_pos = matrix_to_pos(robot_to_target_matrix)
    robot_to_target_pos_f32 = [float(x) for x in robot_to_target_pos]

    return 0, robot_to_target_pos_f32, controls


def deg_to_rad(degree):
    PI = 3.1415926
    return degree / 180 * PI


def deg_pos_2_rad_pos(degPos):
    # rx, ry, rz为角度
    x, y, z, rx, ry, rz = degPos
    rx = deg_to_rad(rx)
    ry = deg_to_rad(ry)
    rz = deg_to_rad(rz)
    return [x, y, z, rx, ry, rz]


def matrix_to_pos(matrix4d, **options):
    mode = options.get("mode", 'ZYX')
    x = matrix4d[0, 3]
    y = matrix4d[1, 3]
    z = matrix4d[2, 3]
    matrix3d = matrix4d[0:3, 0:3]

    rpy = Rotation.from_matrix(matrix3d).as_euler(mode)
    rx = rpy[0]
    ry = rpy[1]
    rz = rpy[2]

    res = [x, y, z, rx, ry, rz]
    return res


def pos_to_matrix(pos, **options):
    x, y, z, rx, ry, rz = pos
    mode = options.get("mode", 'ZYX')
    matrix_3d = Rotation.from_euler(mode, [rx, ry, rz]).as_matrix()

    arr_xyz = np.array([x, y, z]).reshape(3, 1)
    matrix_4d = np.hstack((matrix_3d, arr_xyz))
    arr_aux = np.array([0, 0, 0, 1]).reshape(1, 4)
    matrix_4d_2 = np.vstack((matrix_4d, arr_aux))

    return matrix_4d_2
