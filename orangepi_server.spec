# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['orangepi_server.py'],
    pathex=[],
    binaries=[('/home/<USER>/anaconda3/envs/fastdeploy/lib/python3.9/site-packages/dist-packages/PIL/_imaging.cpython-39-aarch64-linux-gnu.so', '.')],
    datas=[('config.json', '_internal'), ('/home/<USER>/anaconda3/envs/fastdeploy/lib/python3.9/site-packages/rknnlite', 'rknnlite')],
    hiddenimports=['rknnlite', 'rknnlite.api.rknn_utils', 'rknnlite.api.rknn_model', 'rknnlite.utils', 'rknnlite.utils.yaml_parser', 'ruamel', 'ruamel.yaml', 'PIL._tkinter_finder', 'tkinter', 'PIL'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='orangepi_server',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='orangepi_server',
)
