#!/usr/bin/python
# -*- coding: utf-8 -*-

import numpy as np
import typing as t
from abc import abstractmethod


class Segmentation(object):
    """ Abstract segmentation model class. """

    def __init__(self, model_path: t.Optional[str] = None, **options) -> None:
        self._model_path = model_path
        self._options = options

    @abstractmethod
    def main_point_localization(
        self,
        img: np.ndarray,
        **options
    ) -> t.<PERSON>[t.<PERSON>tional[float], t.<PERSON>tional[int], t.Optional[int], t.Optional[t.List[t.List[int]]],t.Any]:
        """ Abstract method for the main point localization. It returns the tube's rotation angle, x-pixel offset,
            y-pixel offset, as well as the tube's polygon contour.
        """
        pass
