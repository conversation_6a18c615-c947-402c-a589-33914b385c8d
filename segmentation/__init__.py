#!/usr/bin/python
# -*- coding: utf-8 -*-

import typing as t


def get_segmentation_model(model_type: str) -> t.Any:
    """ Returns the segmentation model calss variable given its type. """
    if model_type == "yolo":
        from .segmentation_yolo import SegmentationYOLO
        return SegmentationYOLO
    elif model_type == "paddle":
        from .segmentation_paddle import SegmentationPaddle
        return SegmentationPaddle
    else:
        raise AssertionError("Invalid input model type.")
