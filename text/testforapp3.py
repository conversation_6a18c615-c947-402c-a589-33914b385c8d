#coding=gbk
#coding:utf-8
from models.MMR.utils import ForwardHook, cal_anomaly_map, each_patch_loss_function, mmr_adjust_learning_rate
from models.MMR import MMR_base,MMR_pipeline_
from torchvision import transforms
from torchvision.datasets.folder import default_loader
import torch
import cv2
import numpy as np
from PIL import Image
from utils import setup_logging, load_config, parse_args
from scipy.ndimage import gaussian_filter
from utils import compute_pixelwise_retrieval_metrics, compute_pro, save_image, save_video_segmentations
import random
import seaborn as sns
import os
import gc

import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt


#from clip_sam import model_sam, sam_image, predict_sam, model_clip, predict
from clip_sam import *

# 全局变量存储模型，避免重复加载
_mmr_model = None
_cur_model = None
_clip_model = None
_clip_preprocess = None
_clip_adapter = None
_device = None
_cfg = None

def initialize_models():
    """初始化所有模型，只在第一次调用时执行"""
    global _mmr_model, _cur_model, _clip_model, _clip_preprocess, _clip_adapter, _device, _cfg

    if _mmr_model is not None:
        return  # 模型已经初始化

    print("Initializing models for the first time...")

    path_to_config = "./method_config/AeBAD_S/MMR.yaml"
    args = parse_args()
    _cfg = load_config(args, path_to_config=path_to_config)
    _device = 'cuda' if torch.cuda.is_available() else 'cpu'

    # 加载MMR模型
    print("Loading MMR models...")
    _mmr_model = torch.load("./checkpoints/full_model/MMR_epoch_199.pth").to(_device)
    _cur_model = torch.load("./checkpoints/full_model/cur_model.pth").to(_device)
    _mmr_model.eval()
    _cur_model.eval()

    # 加载CLIP模型
    print("Loading CLIP model...")
    clip_checkpoint = './clip_sam/clip/ViT-L-14.pt'
    cache_keys_path = './clip_sam/clip/best_F_16shots.pt'
    _clip_model, _clip_preprocess, _clip_adapter = model_clip(clip_checkpoint, cache_keys_path)

    print("All models initialized successfully!")


def cleanup_gpu_memory():
    """更彻底的GPU内存清理函数"""
    if torch.cuda.is_available():
        # 先同步所有CUDA操作
        torch.cuda.synchronize()
        # 清空缓存
        torch.cuda.empty_cache()
        # 强制垃圾回收
        gc.collect()
        # 再次同步和清空
        torch.cuda.synchronize()
        torch.cuda.empty_cache()

        # 打印当前内存使用情况（调试用）
        allocated = torch.cuda.memory_allocated() / 1024 ** 3  # GB
        reserved = torch.cuda.memory_reserved() / 1024 ** 3  # GB
        print(f"GPU Memory: Allocated={allocated:.2f}GB, Reserved={reserved:.2f}GB")

def aggressive_cleanup():
    """更激进的内存清理，用于函数结束时"""
    import gc
    import matplotlib.pyplot as plt

    # 清理matplotlib
    plt.close('all')
    plt.clf()

    # 多次垃圾回收
    for _ in range(3):
        gc.collect()

    # 清理GPU内存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()
        # 重置GPU内存统计
        torch.cuda.reset_peak_memory_stats()
        torch.cuda.reset_accumulated_memory_stats()

    print("Aggressive cleanup completed")

def mmrr(image):
    """优化后的mmrr函数，包含内存管理"""
    global _mmr_model, _cur_model, _clip_model, _clip_preprocess, _clip_adapter, _device, _cfg

    try:
        # 初始化模型（只在第一次调用时执行）
        initialize_models()

        # 使用全局变量
        device = _device
        cfg = _cfg

        cleanup_gpu_memory()

        #resize = 256
        resize = 224
        imagesize = 224
        IMAGENET_MEAN = [0.485, 0.456, 0.406]
        IMAGENET_STD = [0.229, 0.224, 0.225]

        imag = image
        imag = Image.fromarray(cv2.cvtColor(image,cv2.COLOR_BGR2RGB))
        ori_imag = image

        width,height = imag.size
        print('imag.size===================================================================',width,height)

        # for test
        transform_img = [
            transforms.Resize((resize, resize)),
            transforms.ToTensor()
        ]

        transform_img_no_norm = transforms.Compose(transform_img)
        imag_no_norm = transform_img_no_norm(imag)

        transform_img.append(transforms.Normalize(mean=IMAGENET_MEAN, std=IMAGENET_STD))
        transform_img = transforms.Compose(transform_img)
        imag = transform_img(imag).to(device)
        imag = imag.unsqueeze(0)
        print('imag.shape = ',imag.shape)
  

        print('Using pre-loaded models...')

        # 使用全局模型变量
        mmr_model = _mmr_model
        cur_model = _cur_model

        teacher_outputs_dict = {}
        for extract_layer in cfg.TRAIN.MMR.layers_to_extract_from:
            forward_hook = ForwardHook(teacher_outputs_dict, extract_layer)
            network_layer = cur_model.__dict__["_modules"][extract_layer]
            network_layer[-1].register_forward_hook(forward_hook)

        # 第一阶段：获取正向特征
        with torch.no_grad():
            _ = cur_model(imag)
            # 提取正向特征并立即移到CPU
            multi_scale_features = [teacher_outputs_dict[key].detach().cpu().clone() for key in
                                    cfg.TRAIN.MMR.layers_to_extract_from]
            # 清理teacher_outputs_dict
            teacher_outputs_dict.clear()
            cleanup_gpu_memory()

        # 第二阶段：获取反向特征
        with torch.no_grad():
            m_output = mmr_model(imag, mask_ratio=cfg.TRAIN.MMR.test_mask_ratio)
            # 立即提取需要的特征并分离计算图，移到CPU
            multi_scale_reverse_features = [m_output[key].detach().cpu().clone() for key in
                                            cfg.TRAIN.MMR.layers_to_extract_from]
            del m_output
            cleanup_gpu_memory()

        print(len(multi_scale_reverse_features))
        print('cur_model output = \n', multi_scale_reverse_features[0].shape)

        # 第三阶段：计算异常图 - 需要将特征临时移回GPU进行计算
        with torch.no_grad():
            # 将特征临时移回GPU进行计算
            gpu_multi_scale_features = [feat.to(device) for feat in multi_scale_features]
            gpu_multi_scale_reverse_features = [feat.to(device) for feat in multi_scale_reverse_features]

            anomaly__map, a_map__list = cal_anomaly_map(gpu_multi_scale_features, gpu_multi_scale_reverse_features,
                                                        imag.shape[-1], amap_mode='a')

            # 将结果移到CPU并立即删除GPU版本
            anomaly__map = anomaly__map.detach().cpu().numpy() if torch.is_tensor(anomaly__map) else anomaly__map
            if a_map__list is not None:
                del a_map__list

            # 立即删除GPU上的临时特征
            del gpu_multi_scale_features, gpu_multi_scale_reverse_features
            cleanup_gpu_memory()

        print('anomaly__map.shape = ', anomaly__map.shape)
        print('anomaly__map = ', anomaly__map)

        # 删除CPU上的特征
        del multi_scale_features, multi_scale_reverse_features
        cleanup_gpu_memory()

        for item in range(len(anomaly__map)):
            anomaly__map[item] = gaussian_filter(anomaly__map[item], sigma=4)

        labels_prediction = []
        masks_prediction = []
        labels_prediction.extend(np.max(anomaly__map.reshape(anomaly__map.shape[0], -1), axis=1))
        masks_prediction.extend(anomaly__map.tolist())

        print(labels_prediction)
        print(type(masks_prediction))
        print(len(masks_prediction[0][0]))

        masks_prediction = np.stack(masks_prediction)
        print('mask shape =',masks_prediction.shape)
        segmentations = masks_prediction
        print(type(segmentations))
        print(' segmentations= ',segmentations)
        sample_num = len(segmentations)
        print(sample_num)

        # 清理中间变量
        del labels_prediction, masks_prediction
        cleanup_gpu_memory()

        segmentations_max, segmentations_min = np.max(segmentations), np.min(segmentations)

        cfg.TEST.VISUALIZE.Random_sample = False
        if cfg.TEST.VISUALIZE.Random_sample:
            sample_idx = random.sample(range(sample_num), cfg.TEST.VISUALIZE.Sample_num)
        else:
            sample_idx = [i for i in range(sample_num)]
        print('1111111111111111111111111111111',sample_idx)
        segmentations_random_sample = [segmentations[idx_random] for idx_random in sample_idx]
        print(len(segmentations_random_sample[0][0]))

        # 将tensor移到CPU并转换
        with torch.no_grad():
            original_ima = imag_no_norm.detach().cpu()
            # 立即删除GPU版本
            del imag, imag_no_norm
            cleanup_gpu_memory()
        original_ima = original_ima.squeeze(0)
        original_ima = (original_ima.numpy() * 255).astype(np.uint8).transpose(1, 2, 0)
        original_ima = cv2.cvtColor(original_ima, cv2.COLOR_BGR2RGB)

        # 创建图形时使用上下文管理器
        fig = plt.figure(figsize=(11, 10))
        try:
            #seg_each
            sns.heatmap(segmentations_random_sample[0], vmin=0, vmax=1, xticklabels=False,
                        yticklabels=False, cmap="jet", cbar=True)
            plt.savefig(os.path.join('./', '{test}_sns_heatmap.jpg'),
                        bbox_inches='tight', pad_inches=0.0)
        finally:
            plt.close(fig)
            plt.clf()
            # 强制清理matplotlib内存
            import matplotlib.pyplot as plt
            plt.close('all')
            import gc
            gc.collect()

        # min-max normalize for all images
        seg_each = (segmentations_random_sample[0] - 0) / (1 - 0)

        print(' seg_each = ',seg_each,'\n')
        # only for seg_each that range in (0, 1)
        seg_each = np.clip(seg_each * 255, 0, 255).astype(np.uint8)
        heatmap = cv2.applyColorMap(seg_each, cv2.COLORMAP_JET)
        print('heatmap = ',heatmap)

        if heatmap.shape != original_ima.shape:
            raise Exception("ima shape is not consistent!")

        heatmap_on_image = np.float32(heatmap) / 255 + np.float32(original_ima) / 255
        heatmap_on_image = heatmap_on_image / np.max(heatmap_on_image)
        heatmap_on_image = np.uint8(255 * heatmap_on_image)

        def cv2_ima_save(dir_path,file_name,ori_ima,heat_ima,heat_on_ima):
            cv2.imwrite(os.path.join(dir_path, f'{file_name}_original.jpg'), ori_ima)
            cv2.imwrite(os.path.join(dir_path, f'{file_name}_heatmap.jpg'), heat_ima)
            cv2.imwrite(os.path.join(dir_path, f'{file_name}_hm_on_ima.jpg'), heat_on_ima)

        cv2_ima_save('./test_for_demo', 'hahhaha',ori_ima=original_ima, heat_ima=heatmap, heat_on_ima=heatmap_on_image )

        print('save DONE')
        plt.clf()
        plt.close('all')

        # 清理更多中间变量
        del segmentations, segmentations_random_sample, seg_each
        cleanup_gpu_memory()

        def cv2_crop(imag,x,y,w,h):
            imag = imag[y:y+h, x:x+w]
            return imag

        def cv2_ori_crop(imag,x,y,w,h):
            imag = imag[int(y*height/224): int((y+h)*height/224), int(x*width/224): int((x+w)*width/224)]
            return imag

        heat2gray = cv2.cvtColor(heatmap, cv2.COLOR_BGR2GRAY)

        print('heat2gray ===========================================================================',heat2gray)
        print('heat2gray.shape=========================================',heat2gray.shape)
        print('heat2gray.type', type(heat2gray))
        print('argmax====',np.argmax(heat2gray))
        print('unravel_Max_index=========',np.unravel_index(np.argmax(heat2gray),heat2gray.shape))
        ##### edit
        ret , thresh = cv2.threshold(heat2gray, 223, 255, cv2.THRESH_BINARY)
        contours, hierarchy = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        a=0
        rect = [ ]

        classif_dict = {'a photo of blocked round hole.': [],
                        'a photo of concave edge.': [],
                        'a photo of crack.': [],
                        'a photo of paint coating peeling.': [],
                        'a photo of scratch.': []
                        }
        for c in contours:
            if len(c)<10:
                continue
            x, y, w, h =cv2.boundingRect(c)
            rect.append([int(y*height/224), int((y+h)*height/224), int(x*width/224), int((x+w)*width/224)])
            cv2.rectangle(heatmap, (x, y), (x+w, y+h), (0, 0, 255), 2)
            crop_image = cv2_crop(heatmap, x, y, w, h)
            cv2.imwrite('./crop_images/t.jpg', crop_image )
            cv2.imwrite('./crop_images/q.jpg', heatmap )
      
            crop_ori =  cv2_ori_crop(ori_imag,x,y,w,h)
            print('ori_imag===========================================================================',crop_ori.shape,x,y,w,h)
            cv2.imwrite(f'./crop_images/xxxxx{a}.jpg', crop_ori )

            # 使用预加载的CLIP模型
            cache_values_path = './clip_sam/clip/values_16shots.pt'
            beta = 0.4
            alpha = 4.27

            # 使用全局CLIP模型变量，避免重复加载
            model = _clip_model
            preprocess = _clip_preprocess
            adapter = _clip_adapter

            img = cv2.imread(f'./crop_images/xxxxx{a}.jpg')

            # 转换为PIL Image
            pil_img = Image.fromarray(img)

            text_descriptions = ['a photo of blocked round hole.',
                               'a photo of concave edge.',
                               'a photo of crack.',
                               'a photo of paint coating peeling.',
                               'a photo of scratch.']

            # 使用with torch.no_grad()包裹预测过程
            with torch.no_grad():
                probs = predict_clip(model, preprocess, pil_img, text_descriptions, adapter, beta, alpha,
                                     cache_values_path)
                # 立即将结果移到CPU
                prob = probs.detach().cpu().numpy()

            # 不再需要的变量立即删除
            del probs, pil_img, img

            classify = text_descriptions[np.argmax(prob)]
            classif_dict[classify].append(rect[a])
            print(classif_dict)

            # 每处理一个contour就清理一次内存
            cleanup_gpu_memory()

            a = a + 1

            print()

        print('crop done')
        # 保存返回值的副本
        result_rect = rect.copy() if 'rect' in locals() else []
        result_classif_dict = {k: v.copy() for k, v in classif_dict.items()} if 'classif_dict' in locals() else {}

        # 最终清理所有剩余变量
        try:
            del heatmap, heatmap_on_image, original_ima, heat2gray, thresh, contours
            # 清理其他可能遗漏的变量
            if 'segmentations_max' in locals():
                del segmentations_max
            if 'segmentations_min' in locals():
                del segmentations_min
            if 'sample_idx' in locals():
                del sample_idx
            if 'rect' in locals():
                del rect
            if 'classif_dict' in locals():
                del classif_dict
        except:
            pass
        cleanup_gpu_memory()

        return result_rect, result_classif_dict

    except Exception as e:
        print(f"Error in mmrr function: {e}")
        # 确保在异常情况下也清理内存
        cleanup_gpu_memory()
        raise e

    finally:
        # 最终清理 - 更彻底
        try:
            # 获取当前局部变量
            current_locals = list(locals().keys())
            locals_to_clear = ['heatmap', 'heatmap_on_image', 'original_ima', 'heat2gray',
                               'thresh', 'contours', 'segmentations_max', 'segmentations_min',
                               'sample_idx', 'rect', 'classif_dict', 'segmentations',
                               'segmentations_random_sample', 'seg_each', 'anomaly__map',
                               'a_map__list', 'labels_prediction', 'masks_prediction',
                               'multi_scale_features', 'multi_scale_reverse_features']
            # 只删除存在的变量
            for var_name in locals_to_clear:
                if var_name in current_locals:
                    try:
                        exec(f'del {var_name}')
                    except:
                        pass
        except:
            pass
        # 多次清理确保彻底
        for _ in range(3):
            cleanup_gpu_memory()

        # 最终激进清理
        aggressive_cleanup()
        print("Memory cleanup completed")
  
