{"cells": [{"cell_type": "code", "execution_count": 5, "id": "9e2115d6-972c-4718-8beb-c674f91f9f07", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["943\n"]}, {"ename": "error", "evalue": "OpenCV(4.11.0) /io/opencv/modules/highgui/src/window.cpp:1295: error: (-2:Unspecified error) The function is not implemented. Rebuild the library with Windows, GTK+ 2.x or Cocoa support. If you are on Ubuntu or Debian, install libgtk2.0-dev and pkg-config, then re-run cmake or configure script in function 'cvDestroyAllWindows'\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31<PERSON><PERSON>r\u001b[0m                                     <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 26\u001b[0m\n\u001b[1;32m     24\u001b[0m \u001b[38;5;66;03m# 释放视频流及窗口\u001b[39;00m\n\u001b[1;32m     25\u001b[0m video\u001b[38;5;241m.\u001b[39mrelease()\n\u001b[0;32m---> 26\u001b[0m \u001b[43mcv2\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdestroyAllWindows\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[0;31merror\u001b[0m: OpenCV(4.11.0) /io/opencv/modules/highgui/src/window.cpp:1295: error: (-2:Unspecified error) The function is not implemented. Rebuild the library with Windows, GTK+ 2.x or Cocoa support. If you are on Ubuntu or Debian, install libgtk2.0-dev and pkg-config, then re-run cmake or configure script in function 'cvDestroyAllWindows'\n"]}], "source": ["import cv2\n", "\n", "# 打开视频文件\n", "video = cv2.VideoCapture('./test.mov')\n", "a=0\n", "# 循环遍历视频的每一帧\n", "while video.isOpened():\n", "    # 读取视频的帧\n", "    ret, frame = video.read()\n", "    \n", "    # 如果未成功读取帧，则退出循环\n", "    if not ret:\n", "        break\n", "    #print(frame)\n", "    # 在这里可以对读取到的帧进行处理\n", "    # 例如显示帧，保存帧，进行帧间差分等\n", "    \n", "    # 显示当前帧\n", "    a=a+1\n", "    \n", "    # 按下 'q' 键退出循环\n", " \n", "print(a) \n", "# 释放视频流及窗口\n", "video.release()\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "id": "3149141c-4c7b-4d41-a9e6-d353b94e60fa", "metadata": {"execution": {"iopub.execute_input": "2025-07-17T06:28:04.536647Z", "iopub.status.busy": "2025-07-17T06:28:04.536026Z", "iopub.status.idle": "2025-07-17T06:28:08.710156Z", "shell.execute_reply": "2025-07-17T06:28:08.709126Z", "shell.execute_reply.started": "2025-07-17T06:28:04.536590Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["30\n"]}, {"name": "stderr", "output_type": "stream", "text": ["OpenCV: FFMPEG: tag 0x5634504d/'MP4V' is not supported with codec id 12 and format 'mp4 / MP4 (MPEG-4 Part 14)'\n", "OpenCV: FFMPEG: fallback to use tag 0x7634706d/'mp4v'\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[2], line 35\u001b[0m\n\u001b[1;32m     32\u001b[0m     gray_frame \u001b[38;5;241m=\u001b[39m cv2\u001b[38;5;241m.\u001b[39mcvtColor(frame, cv2\u001b[38;5;241m.\u001b[39mCOLOR_BGR2GRAY)\n\u001b[1;32m     34\u001b[0m     \u001b[38;5;66;03m# 保存灰度帧到输出视频\u001b[39;00m\n\u001b[0;32m---> 35\u001b[0m     \u001b[43mout\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwrite\u001b[49m\u001b[43m(\u001b[49m\u001b[43mgray_frame\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     41\u001b[0m \u001b[38;5;66;03m# 释放视频资源并关闭所有窗口\u001b[39;00m\n\u001b[1;32m     42\u001b[0m cap\u001b[38;5;241m.\u001b[39mrelease()\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["import cv2\n", "\n", "# 指定本地视频路径和输出路径\n", "input_video_path = \"test.mov\"       # 输入视频路径\n", "output_video_path = \"d.mp4\"  # 输出灰度视频路径\n", "\n", "# 打开输入视频文件\n", "cap = cv2.VideoCapture(input_video_path)\n", "\n", "# 检查视频是否成功打开\n", "if not cap.isOpened():\n", "    print(\"Error: Could not open video.\")\n", "    exit()\n", "\n", "# 获取原视频的宽度、高度和帧率\n", "frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))\n", "frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))\n", "fps = int(cap.get(cv2.CAP_PROP_FPS))\n", "a=0\n", "# 创建 VideoWriter 对象，用于保存灰度视频\n", "fourcc = cv2.VideoWriter_fourcc('M', 'P', '4', 'V')  # 视频编码格式\n", "print(fps)\n", "out = cv2.VideoWriter(output_video_path, fourcc, fps, (frame_width, frame_height), isColor=False)\n", "\n", "# 逐帧读取视频、转换为灰度并保存\n", "while cap.isOpened():\n", "    ret, frame = cap.read()\n", "    if not ret:\n", "        break\n", "    \n", "    # 将帧转换为灰度图像\n", "    gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)\n", "    \n", "    # 保存灰度帧到输出视频\n", "    out.write(gray_frame)\n", "    \n", "\n", "\n", "    \n", "\n", "# 释放视频资源并关闭所有窗口\n", "cap.release()\n", "print(\"1\")"]}, {"cell_type": "code", "execution_count": 12, "id": "9aea08bb-9b2a-4338-99d9-85bead6a5927", "metadata": {"execution": {"iopub.execute_input": "2025-07-11T07:07:38.954108Z", "iopub.status.busy": "2025-07-11T07:07:38.953330Z", "iopub.status.idle": "2025-07-11T07:07:55.811785Z", "shell.execute_reply": "2025-07-11T07:07:55.810878Z", "shell.execute_reply.started": "2025-07-11T07:07:38.954047Z"}}, "outputs": [], "source": ["import torch"]}, {"cell_type": "code", "execution_count": 8, "id": "62ff05e2-8c69-40f1-b190-80c1a7de6e9f", "metadata": {"execution": {"iopub.execute_input": "2025-07-17T07:30:45.758112Z", "iopub.status.busy": "2025-07-17T07:30:45.757506Z", "iopub.status.idle": "2025-07-17T07:38:17.593986Z", "shell.execute_reply": "2025-07-17T07:38:17.592925Z", "shell.execute_reply.started": "2025-07-17T07:30:45.758054Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["frame(1920, 1080), fps=29\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "<class 'requests.models.Response'>\n", "<Response [200]>\n", "11111\n"]}], "source": ["import cv2\n", "import base64\n", "import requests\n", "import cv2\n", "import json\n", "import numpy as np\n", "\n", "def getByte(path):\n", "    with open(path, 'rb') as f:\n", "        img_byte = base64.b64encode(f.read())\n", "    img_str = img_byte.decode('ascii')\n", "    return img_str\n", "\n", "import os\n", "\n", "# 指定本地视频路径和输出路径\n", "input_video_path = \"testt.mov\"       # 输入视频路径\n", "output_video_path = \"testt1.mp4\"  # 输出灰度视频路径\n", "\n", "# 打开输入视频文件\n", "cap = cv2.VideoCapture(input_video_path)\n", "\n", "# 检查视频是否成功打开\n", "if not cap.isOpened():\n", "    print(\"Error: Could not open video.\")\n", "    exit()\n", "\n", "# 获取原视频的宽度、高度和帧率\n", "frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))\n", "frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))\n", "fps = int(cap.get(cv2.CAP_PROP_FPS))\n", "\n", "print(f\"frame{frame_width,frame_height}, fps={fps}\")\n", "\n", "# 创建 VideoWriter 对象，用于保存灰度视频\n", "fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # 视频编码格式\n", "out = cv2.VideoWriter(output_video_path, fourcc, fps, (frame_width, frame_height), isColor=False)\n", "a = 0\n", "# 逐帧读取视频、转换为灰度并保存\n", "while cap.isOpened():\n", "    ret, frame = cap.read()\n", "    if not ret:\n", "        break\n", "    \n", "    # 将帧转换为灰度图像\n", "    #gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)\n", "    cv2.imwrite('test.jpg',frame)\n", "    # 保存灰度帧到输出视频\n", "\n", "    img_str = getByte('test.jpg')\n", "    json_str = json.dumps({'image':img_str})\n", "    \n", "    response = requests.post('http://172.20.0.2:8878/mmr',json_str)\n", "    #response = requests.post('http://127.0.0.1:8878/ocr',json_str)\n", "    print(type(response))\n", "    print(response)\n", "    \n", "    #data = str(response.text,'utf-8')\n", "    #data = json.loads(data)\n", "    data = json.loads(response.text)\n", "    \n", "    \n", "    processed_img = cv2.imread('./test.jpg')\n", "    \n", "    \n", "    boxes = data['Result']['rect']\n", "    \n", "    for idx, box in enumerate(boxes, start=1):\n", "        try:\n", "            y1, y2, x1, x2 = map(int, box)\n", "            if not (0 <= x1 < x2 <= processed_img.shape[1] and 0 <= y1 < y2 <= processed_img.shape[0]):\n", "                print(f\"无效坐标: {x1},{y1} - {x2},{y2}\")\n", "                continue\n", "    \n", "            cv2.rectangle(processed_img, (x1, y1), (x2, y2), (0, 0, 255), 5)\n", "            \n", "        except Exception as box_error:\n", "            print(f\"矩形框处理异常: {box_error}\")    \n", "    cv2.imwrite('./hahah.jpg',processed_img)\n", "\n", "    \n", "    out.write(processed_img)\n", "    a=a+1\n", "    if a ==fps:\n", "        break\n", "\n", "\n", "# 释放视频资源并关闭所有窗口\n", "cap.release()\n", "#cv2.destroyAllWindows()\n", "print('11111')"]}, {"cell_type": "code", "execution_count": 10, "id": "f4848052-998e-4b95-8212-48e02e6165bd", "metadata": {"execution": {"iopub.execute_input": "2025-07-17T07:43:47.839483Z", "iopub.status.busy": "2025-07-17T07:43:47.838820Z", "iopub.status.idle": "2025-07-17T07:44:02.494069Z", "shell.execute_reply": "2025-07-17T07:44:02.492888Z", "shell.execute_reply.started": "2025-07-17T07:43:47.839425Z"}}, "outputs": [], "source": ["import cv2\n", "\n", "# 指定本地视频路径和输出路径\n", "input_video_path = \"testt.mov\"       # 输入视频路径\n", "output_video_path = \"01_gray.mov\"  # 输出灰度视频路径\n", "\n", "# 打开输入视频文件\n", "cap = cv2.VideoCapture(input_video_path)\n", "\n", "# 检查视频是否成功打开\n", "if not cap.isOpened():\n", "    print(\"Error: Could not open video.\")\n", "    exit()\n", "\n", "# 获取原视频的宽度、高度和帧率\n", "frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))\n", "frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))\n", "fps = int(cap.get(cv2.CAP_PROP_FPS))\n", "\n", "# 创建 VideoWriter 对象，用于保存灰度视频\n", "fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # 视频编码格式\n", "out = cv2.VideoWriter(output_video_path, fourcc, fps, (frame_width, frame_height), isColor=False)\n", "\n", "# 逐帧读取视频、转换为灰度并保存\n", "while cap.isOpened():\n", "    ret, frame = cap.read()\n", "    if not ret:\n", "        break\n", "\n", "    # 将帧转换为灰度图像\n", "    gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)\n", "\n", "    # 保存灰度帧到输出视频\n", "    out.write(gray_frame)\n", "\n", "\n", "# 释放视频资源并关闭所有窗口\n", "cap.release()\n"]}, {"cell_type": "code", "execution_count": null, "id": "be220b76-629e-42b1-b37a-c15b8666f00b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 5}