import cv2
import base64
import requests
import cv2
import json
import numpy as np


def getByte(path):
    with open(path, 'rb') as f:
        img_byte = base64.b64encode(f.read())
    img_str = img_byte.decode('ascii')
    return img_str


import os

# 指定本地视频路径和输出路径
input_video_path = "video.mp4"  # 输入视频路径
output_video_path = "testt1.avi"  # 输出灰度视频路径

# 打开输入视频文件
cap = cv2.VideoCapture(input_video_path)

# 检查视频是否成功打开
if not cap.isOpened():
    print("Error: Could not open video.")
    exit()

# 获取原视频的宽度、高度和帧率
frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
fps = int(cap.get(cv2.CAP_PROP_FPS))

print(f"frame{frame_width, frame_height}, fps={fps}")

# 🔧 修复: 尝试不同的编码格式
# 首先尝试 XVID 编码
fourcc = cv2.VideoWriter_fourcc(*'XVID')
out = cv2.VideoWriter(output_video_path, fourcc, fps, (frame_width, frame_height), isColor=True)

# 如果XVID失败，尝试其他编码
if not out.isOpened():
    print("XVID编码失败，尝试mp4v...")
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_video_path, fourcc, fps, (frame_width, frame_height), isColor=True)

if not out.isOpened():
    print("mp4v编码失败，尝试MJPG...")
    fourcc = cv2.VideoWriter_fourcc(*'MJPG')
    output_video_path = "testt1.avi"  # 改为avi格式
    out = cv2.VideoWriter(output_video_path, fourcc, fps, (1320, frame_height), isColor=True)

# 最终检查
if not out.isOpened():
    print("❌ 错误: 无法创建输出视频文件，尝试了多种编码格式")
    cap.release()
    exit()
else:
    print(f"✅ 成功创建视频写入器，输出文件: {output_video_path}")
    
# cap = cv2.VideoCapture(input_video_path)
# frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
# frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

a = 0
processed_frames = 0

print(f"开始处理视频，原始尺寸: {frame_width} x {frame_height}")

# 逐帧读取视频、转换为灰度并保存
while cap.isOpened():
    ret, frame = cap.read()
    if not ret:
        print(f"\n视频读取完成，共处理 {processed_frames} 帧")
        break

    print(f"正在处理第 {a+1} 帧...", end=' ')

    # 🔧 关键修复: 裁剪图像会改变尺寸，需要重新调整VideoWriter设置
    original_frame = frame.copy()  # 保存原始帧
    cropped_frame = frame[:, 600:1920]  # 裁剪后的帧

    # 获取裁剪后的尺寸
    crop_height, crop_width = cropped_frame.shape[:2]
    print(f"裁剪后尺寸: {crop_width} x {crop_height}")

    # 如果是第一帧，需要重新创建VideoWriter
    if a == 0:
        print(f"\n重新创建VideoWriter，使用裁剪后的尺寸: {crop_width} x {crop_height}")
        out.release()  # 释放之前的VideoWriter

        # 使用裁剪后的尺寸创建新的VideoWriter
        fourcc = cv2.VideoWriter_fourcc(*'XVID')
        out = cv2.VideoWriter(output_video_path, fourcc, fps, (crop_width, crop_height), isColor=True)

        if not out.isOpened():
            print("❌ 无法创建新的VideoWriter")
            break

    cv2.imwrite('test.jpg', cropped_frame)  # 保存裁剪后的帧

    img_str = getByte('test.jpg')
    json_str = json.dumps({'image': img_str})

    # 🔧 修复: 添加API调用错误处理
    try:
        response = requests.post('http://192.168.2.3:8878/mmr', json_str, timeout=1000)

        if response.status_code == 200:
            data = json.loads(response.text)
            print(f"✅ API调用成功，帧 {a + 1}")
        else:
            print(f"❌ API调用失败: {response.status_code}")
            data = {'Result': {'rect': []}}  # 空结果

    except requests.exceptions.RequestException as e:
        print(f"❌ API请求异常: {e}")
        data = {'Result': {'rect': []}}  # 空结果
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析异常: {e}")
        data = {'Result': {'rect': []}}  # 空结果

    # 🔧 修复: 使用裁剪后的帧作为基础，确保尺寸正确
    processed_img = cropped_frame.copy()

    # 检查API响应是否包含检测结果
    if 'Result' in data and 'rect' in data['Result']:
        boxes = data['Result']['rect']

        for idx, box in enumerate(boxes, start=1):
            try:
                y1, y2, x1, x2 = map(int, box)
                # 🔧 修复: 使用裁剪后图像的尺寸进行边界检查
                if not (0 <= x1 < x2 <= crop_width and 0 <= y1 < y2 <= crop_height):
                    print(f"无效坐标: {x1},{y1} - {x2},{y2}")
                    continue

                cv2.rectangle(processed_img, (x1, y1), (x2, y2), (0, 0, 255), 3)

                # 添加标签
                label = f"Object {idx}"
                cv2.putText(processed_img, label, (x1, y1 - 10),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

            except Exception as box_error:
                print(f"矩形框处理异常: {box_error}")
    else:
        print("API响应中没有检测结果")

    # 保存处理后的图像用于调试
    cv2.imwrite('./processed_frame.jpg', processed_img)

    # 🔧 修复: 确保图像尺寸与VideoWriter设置一致（使用裁剪后的尺寸）
    if processed_img.shape[:2] != (crop_height, crop_width):
        processed_img = cv2.resize(processed_img, (crop_width, crop_height))

    # 写入处理后的帧
    out.write(processed_img)
    processed_frames += 1
    print(f"✅ 第{a+1}帧已写入")

    a = a + 1

    # 限制处理帧数（用于测试）
    if a >= 14 * fps:  # 只处理1秒的视频
        print(f"\n已处理 {a} 帧，停止处理")
        break

# 释放视频资源
cap.release()
out.release()  # 🔧 修复: 确保释放VideoWriter资源

print('\n' + '='*50)
print('✅ 视频处理完成!')
print(f'输出文件: {output_video_path}')
print(f'成功处理帧数: {processed_frames}/{a}')
print('='*50)