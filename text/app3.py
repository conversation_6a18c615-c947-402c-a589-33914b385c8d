from flask import Flask, jsonify, request
from testforapp2 import mmrr, initialize_models, cleanup_gpu_memory
from ocr_test import ocrr
import json
import base64
import numpy as np
import cv2
import os
import torch
import gc

# 设置CUDA内存分配策略
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:21"
# 设置CUDA内存分片策略，减少内存碎片
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:128"

app = Flask(__name__)

# 在应用启动时初始化模型
print("Initializing models at startup...")
try:
    initialize_models()
    print("Models initialized successfully!")
except Exception as e:
    print(f"Failed to initialize models: {e}")
    raise e

def getByte(path):
    with open(path, 'rb') as f:
        img_byte = base64.b64encode(f.read())
    img_str = img_byte.decode('ascii')
    return img_str


@app.route("/mmr", methods=["POST"])
def mmr():
    try:
        data = str(request.data,'utf-8')
        data = json.loads(data)
        img_str = data["image"]
        img_decode_ = img_str.encode('ascii')
        img_decode = base64.b64decode(img_decode_)
        img_np = np.frombuffer(img_decode, np.uint8)
        img = cv2.imdecode(img_np, cv2.COLOR_RGB2BGR)

        print("Processing image with MMR...")
        rect, classify_dict = mmrr(img)
        result = {"rect": rect, "classify_dict": classify_dict}

        # 清理局部变量
        del img, img_np, img_decode

        ResponseStatus = {"Message": "success", "StatusCode": "0"}
        return jsonify(ResponseStatus=ResponseStatus,Result=result)

    except Exception as e:
        print(f"Error in /mmr endpoint: {e}")
        cleanup_gpu_memory()  # 确保在错误时也清理内存
        ResponseStatus = {"Message": f"Error: {str(e)}", "StatusCode": "1"}
        return jsonify(ResponseStatus=ResponseStatus, Result={})
    finally:
        cleanup_gpu_memory()

@app.route("/ocr", methods=["POST"])
def ocr():
    try:
        print('enter ocr class')
        data = str(request.data,'utf-8')
        data = json.loads(data)
        img_str = data["image"]
        img_decode_ = img_str.encode('ascii')
        img_decode = base64.b64decode(img_decode_)
        img_np = np.frombuffer(img_decode, np.uint8)
        img = cv2.imdecode(img_np, cv2.COLOR_RGB2BGR)
        result_ocr = ocrr(img)
        print('result_ocr = ',result_ocr)
        result = {"result_ocr": result_ocr}

        # 清理局部变量
        del img, img_np, img_decode

        ResponseStatus = {"Message": "success", "StatusCode": "0"}
        return jsonify(ResponseStatus=ResponseStatus,Result=result)

    except Exception as e:
        print(f"Error in /ocr endpoint: {e}")
        ResponseStatus = {"Message": f"Error: {str(e)}", "StatusCode": "1"}
        return jsonify(ResponseStatus=ResponseStatus, Result={})

@app.route("/memory_status", methods=["GET"])
def memory_status():
    """获取GPU内存使用状态"""
    try:
        if torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated() / 1024**3  # GB
            cached = torch.cuda.memory_reserved() / 1024**3  # GB
            total = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB

            memory_info = {
                "allocated_gb": round(allocated, 2),
                "cached_gb": round(cached, 2),
                "total_gb": round(total, 2),
                "free_gb": round(total - allocated, 2),
                "utilization_percent": round((allocated / total) * 100, 2)
            }
        else:
            memory_info = {"message": "CUDA not available"}

        ResponseStatus = {"Message": "success", "StatusCode": "0"}
        return jsonify(ResponseStatus=ResponseStatus, Result=memory_info)

    except Exception as e:
        ResponseStatus = {"Message": f"Error: {str(e)}", "StatusCode": "1"}
        return jsonify(ResponseStatus=ResponseStatus, Result={})

@app.route("/cleanup_memory", methods=["POST"])
def manual_cleanup():
    """手动清理GPU内存"""
    try:
        cleanup_gpu_memory()
        ResponseStatus = {"Message": "Memory cleanup completed", "StatusCode": "0"}
        return jsonify(ResponseStatus=ResponseStatus, Result={})
    except Exception as e:
        ResponseStatus = {"Message": f"Error: {str(e)}", "StatusCode": "1"}
        return jsonify(ResponseStatus=ResponseStatus, Result={})

if __name__ == "__main__":
    app.run(debug=True, host="0.0.0.0", port=8878)
