# socket_plc.py 整体逻辑梳理

## 📋 整体架构

`socket_plc.py` 是一个专门处理PLC通信的模块，实现了完整的PLC协议解析、状态机管理和响应生成。

## 🔄 主要执行流程

### 1. 服务启动阶段
```python
plc_socket_listener(host, port, localizer)  # 主入口函数
├── 创建Socket服务器，监听2000端口
├── 等待PLC连接
└── 为每个连接创建处理线程
```

### 2. 连接处理阶段
```python
handle_plc_connection(conn, addr, localizer)  # 核心处理函数
├── 循环接收PLC数据
├── 解析和处理数据
├── 生成响应数据
└── 发送响应给PLC
```

## 📥 PLC指令接收流程

### 位置：`handle_plc_connection()` 函数中

```python
# 第363-372行：接收PLC指令的核心代码
while True:
    # 1. 接收48字节数据
    data = recv_exact(conn, 48)
    
    # 2. 解析PLC数据
    parsed_data = parse_plc_data(data)
    
    # 3. 更新全局状态
    current_mode = parsed_data["mode"]
    heartbeat_counter = (heartbeat_counter + 1) % 256
```

### 数据解析：`parse_plc_data()` 函数

```python
# 第67-120行：解析48字节PLC数据
def parse_plc_data(data: bytes) -> dict:
    # 前24字节：6个float32坐标
    pos_floats = struct.unpack("!6f", data[:24])
    
    # 25-42字节：控制位
    controls = struct.unpack("!18B", data[24:42])
    
    # 解析关键控制位
    heartbeat = controls[0]     # 25位：心跳
    manual_flag = controls[1]   # 26位：手动模式
    auto_flag = controls[2]     # 27位：自动模式
    
    # 解析各种控制标志
    flags = {
        "plc_ready_clamp": controls[4] == 255,      # 29位：PLC准备入夹
        "not_in_position": controls[5] == 255,      # 30位：未到位
        "in_position": controls[6] == 255,          # 31位：到位
        "start_judgment": controls[7] == 255,       # 32位：开始判定
        "result_received": controls[11] == 255,     # 36位：结果确认
        # ... 更多标志
    }
```

## 📤 PLC指令返回流程

### 位置：`handle_plc_connection()` 函数中

```python
# 第406-410行：发送响应的核心代码
# 1. 根据模式处理状态机
if current_mode == "manual":
    response_flags = handle_manual_mode_state_machine(parsed_data, localizer)
elif current_mode == "auto":
    response_flags = handle_auto_mode_state_machine(parsed_data, localizer)

# 2. 打包响应数据
response_data = pack_data_to_plc(current_pos, response_flags, heartbeat_counter)

# 3. 发送给PLC
conn.sendall(response_data)
```

### 响应打包：`pack_data_to_plc()` 函数

```python
# 第422-460行：打包48字节响应数据
def pack_data_to_plc(pos: list[float], flags: dict, heartbeat: int = 0) -> bytes:
    # 前24字节：6个float32坐标
    pos_bytes = struct.pack("!6f", *pos)
    
    # 25-42字节：控制位响应
    control_list = [
        heartbeat,                                         # 25位：心跳
        255 if flags.get("manual", False) else 0,          # 26位：手动模式确认
        255 if flags.get("auto", False) else 0,            # 27位：自动模式确认
        flags.get("preparing", 0),                         # 28位：准备状态(9=准备中)
        flags.get("ready", 0),                             # 29位：就绪状态(10=就绪)
        flags.get("clamp_ack", 0),                         # 30位：入夹确认(20=确认)
        flags.get("judging", 0),                           # 31位：判定状态(30=判定中)
        flags.get("judgment_complete", 0),                 # 32位：判定完成(40=完成)
        # ... 其他控制位
        255 if flags.get("result_ng", False) else 0,       # 40位：NG结果
        255 if flags.get("result_ok", False) else 0,       # 41位：OK结果
    ]
```

## 🔄 状态机处理

### 自动模式状态机：`handle_auto_mode_state_machine()`

```python
# 第171-353行：自动模式状态机逻辑
def handle_auto_mode_state_machine(parsed_data: dict, localizer=None) -> dict:
    global auto_state, last_judgment_result
    
    if auto_state == "idle":
        # 启动自动模式 -> 返回28位=9(准备中)
        auto_state = "preparing"
        return {"auto": True, "preparing": 9}
        
    elif auto_state == "preparing":
        # 准备完成 -> 返回29位=10(就绪)
        auto_state = "ready"
        return {"auto": True, "ready": 10}
        
    elif auto_state == "ready":
        # 等待PLC准备入夹(29位=255) -> 返回30位=20(确认)
        if flags.get("plc_ready_clamp"):
            auto_state = "plc_ready"
            return {"auto": True, "clamp_ack": 20}
            
    # ... 更多状态转换
```

### 手动模式状态机：`handle_manual_mode_state_machine()`

```python
# 第120-170行：手动模式状态机逻辑
def handle_manual_mode_state_machine(parsed_data: dict, localizer=None) -> dict:
    # 手动模式的状态处理逻辑
    # 目前实现较简单，可根据需要扩展
```

## 🔧 关键函数说明

### 1. 网络通信函数
- **`plc_socket_listener()`**：主监听函数，创建服务器socket
- **`handle_plc_connection()`**：处理单个PLC连接的主循环
- **`recv_exact()`**：精确接收指定字节数的数据

### 2. 数据处理函数
- **`parse_plc_data()`**：解析PLC发来的48字节数据
- **`pack_data_to_plc()`**：打包发送给PLC的48字节响应

### 3. 状态机函数
- **`handle_auto_mode_state_machine()`**：处理自动模式状态转换
- **`handle_manual_mode_state_machine()`**：处理手动模式状态转换

### 4. 业务逻辑函数
- **`perform_vision_judgment()`**：执行视觉判定
- **`load_last_offset()`**：加载偏移量配置

## 🔄 数据流向

```
PLC设备 
    ↓ (发送48字节命令)
recv_exact() 
    ↓ (接收数据)
parse_plc_data() 
    ↓ (解析命令)
状态机处理 
    ↓ (生成响应标志)
pack_data_to_plc() 
    ↓ (打包响应)
conn.sendall() 
    ↓ (发送48字节响应)
PLC设备
```

## 📍 补充内容的建议位置

### 1. 添加新的控制逻辑
- **位置**：状态机函数中 (`handle_auto_mode_state_machine` 或 `handle_manual_mode_state_machine`)
- **用途**：添加新的状态转换、业务逻辑处理

### 2. 添加新的数据解析
- **位置**：`parse_plc_data()` 函数中
- **用途**：解析新的控制位、添加新的标志位

### 3. 添加新的响应数据
- **位置**：`pack_data_to_plc()` 函数中
- **用途**：添加新的响应控制位、状态码

### 4. 添加新的业务功能
- **位置**：创建新的独立函数，在状态机中调用
- **用途**：视觉处理、设备控制、数据记录等

### 5. 添加错误处理
- **位置**：`handle_plc_connection()` 的异常处理部分
- **用途**：连接恢复、状态重置、错误记录

## 🎯 核心要点

1. **接收PLC指令**：在 `handle_plc_connection()` 的主循环中通过 `recv_exact()` 接收
2. **返回PLC指令**：在同一循环中通过 `conn.sendall()` 发送响应
3. **状态管理**：通过全局变量和状态机函数管理系统状态
4. **数据格式**：固定48字节格式，前24字节坐标，后24字节控制位
5. **线程安全**：使用 `plc_state_lock` 保护全局状态变量
