import socket
import struct

def connect_to_server(server_ip,server_port):
    # 创建套接字
    client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    client_socket.connect((server_ip, server_port))
    print(f"Connected to server {server_ip}:{server_port}")
    return client_socket

def disconnect(client_socket):
    client_socket.close()
    print("Disconnected from server")

def send_command(client_socket, command):
    """
    连接服务端并发送命令，接收返回结果
    :param server_ip: 服务端 IP 地址
    :param server_port: 服务端端口
    :param command: 发送的命令字符串
    """
    try:
        # 发送命令
        client_socket.sendall(command.encode('utf-8'))
        print(f"Sent command: {command.strip()}")

        # 接收响应
        response = client_socket.recv(1024)

        # 解析响应
        if command.startswith("$1"):
            # 解析相机状态返回值
            success = struct.unpack("!i", response)[0]
            print(f"Camera connected: {'Yes' if success == 1 else 'No'}")
        elif command.startswith("$2"):
            # 解析图像处理返回值
            data = struct.unpack("!i6f", response)
            success, pose = data[0], data[1:]
            print(f"Processing success: {'Yes' if success == 1 else 'No'}")
            print(f"Pose: {pose}")
        else:
            print("Unknown response format")
    except Exception as e:
        print(f"Error: {e}")



if __name__ == "__main__":
    # 设置服务端 IP 和端口（修改为服务端的实际 IP 和端口）
    SERVER_IP = "*************"  # 替换为服务端 IP
    SERVER_PORT = 8000           # 替换为服务端端口

    client_socket = connect_to_server(SERVER_IP,SERVER_PORT)

    # 提示用户输入测试命令
    print("Test commands:")
    print("$1\\r\\n - Check camera connection status")
    print("$2\\r\\n - Run image processing and get results")
    # command = input("Enter command: ")
    command_1 = "$1\\r\\n"
    command_2 = "$2\\r\\n"
    try:
        while True:
            command_id = input("Enter command: ")

            if "1" in command_id:
                command = command_1
                send_command(client_socket, command.strip())
            elif "2" in command_id:
                command = command_2
                send_command(client_socket, command.strip())

            # send_command(SERVER_IP, SERVER_PORT, command.strip())

    except KeyboardInterrupt:
        print('disconnect')
        disconnect(client_socket)
