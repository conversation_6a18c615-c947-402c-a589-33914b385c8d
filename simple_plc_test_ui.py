#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版PLC信号测试UI
"""

import sys
import socket
import struct
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QGridLayout, QWidget, QPushButton, QLabel, QCheckBox, 
                             QLineEdit, QGroupBox, QSpinBox)
from PyQt5.QtCore import QTimer
from PyQt5.QtGui import QFont

class SimplePLCTestUI(QMainWindow):
    """简化版PLC测试UI"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PLC信号测试工具 - 简化版")
        self.setGeometry(100, 100, 1200, 800)
        
        # 连接状态
        self.connected = False
        self.socket = None
        
        # 控制位状态
        self.control_bits = {}
        self.response_labels = {}
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QHBoxLayout(central_widget)
        
        # 左侧：发送控制
        left_layout = QVBoxLayout()
        
        # 连接控制
        conn_group = QGroupBox("连接控制")
        conn_layout = QHBoxLayout(conn_group)
        
        self.ip_input = QLineEdit("127.0.0.1")
        self.port_input = QSpinBox()
        self.port_input.setRange(1, 65535)
        self.port_input.setValue(2000)
        
        self.connect_btn = QPushButton("连接")
        self.connect_btn.clicked.connect(self.toggle_connection)
        
        conn_layout.addWidget(QLabel("IP:"))
        conn_layout.addWidget(self.ip_input)
        conn_layout.addWidget(QLabel("端口:"))
        conn_layout.addWidget(self.port_input)
        conn_layout.addWidget(self.connect_btn)
        
        left_layout.addWidget(conn_group)
        
        # 坐标控制
        coord_group = QGroupBox("坐标数据")
        coord_layout = QGridLayout(coord_group)
        
        self.coord_inputs = []
        coord_labels = ["X", "Y", "Z", "RX", "RY", "RZ"]
        for i, label in enumerate(coord_labels):
            coord_layout.addWidget(QLabel(f"{label}:"), i//3, (i%3)*2)
            input_box = QLineEdit("0.0")
            self.coord_inputs.append(input_box)
            coord_layout.addWidget(input_box, i//3, (i%3)*2+1)
        
        left_layout.addWidget(coord_group)
        
        # 控制位
        control_group = QGroupBox("控制位 (25-42)")
        control_layout = QGridLayout(control_group)
        
        # 重要的控制位
        important_bits = {
            25: ("心跳", "spinbox"),
            26: ("手动模式", "checkbox"),
            27: ("自动模式", "checkbox"),
            29: ("PLC准备入夹", "checkbox"),
            30: ("未到位", "checkbox"),
            31: ("到位", "checkbox"),
            32: ("开始判定", "checkbox"),
            36: ("结果确认", "checkbox"),
        }
        
        row = 0
        for bit, (name, widget_type) in important_bits.items():
            control_layout.addWidget(QLabel(f"{bit}位 {name}:"), row, 0)
            
            if widget_type == "checkbox":
                widget = QCheckBox()
                widget.stateChanged.connect(lambda state, b=bit: self.update_control_bit(b, state == 2))
            else:
                widget = QSpinBox()
                widget.setRange(0, 255)
                widget.valueChanged.connect(lambda value, b=bit: self.update_control_bit(b, value))
            
            self.control_bits[bit] = widget
            control_layout.addWidget(widget, row, 1)
            row += 1
        
        left_layout.addWidget(control_group)
        
        # 快捷按钮
        shortcut_group = QGroupBox("快捷操作")
        shortcut_layout = QGridLayout(shortcut_group)
        
        shortcuts = [
            ("清空", self.clear_all),
            ("手动模式", self.set_manual),
            ("自动模式", self.set_auto),
            ("PLC准备", self.set_plc_ready),
            ("到位", self.set_in_position),
            ("开始判定", self.set_judgment),
        ]
        
        for i, (text, func) in enumerate(shortcuts):
            btn = QPushButton(text)
            btn.clicked.connect(func)
            shortcut_layout.addWidget(btn, i//3, i%3)
        
        left_layout.addWidget(shortcut_group)
        
        # 发送按钮
        self.send_btn = QPushButton("发送数据")
        self.send_btn.clicked.connect(self.send_data)
        self.send_btn.setEnabled(False)
        self.send_btn.setStyleSheet("QPushButton { background-color: lightblue; font-weight: bold; padding: 10px; }")
        left_layout.addWidget(self.send_btn)
        
        # 右侧：响应显示
        right_layout = QVBoxLayout()
        
        # 状态显示
        self.status_label = QLabel("状态: 未连接")
        self.status_label.setStyleSheet("QLabel { background-color: red; color: white; padding: 5px; }")
        right_layout.addWidget(self.status_label)
        
        # 响应坐标
        resp_coord_group = QGroupBox("响应坐标")
        resp_coord_layout = QGridLayout(resp_coord_group)
        
        self.coord_response_labels = []
        for i, label in enumerate(coord_labels):
            resp_coord_layout.addWidget(QLabel(f"{label}:"), i//3, (i%3)*2)
            response_label = QLabel("0.0")
            response_label.setStyleSheet("QLabel { background-color: lightgray; padding: 3px; }")
            self.coord_response_labels.append(response_label)
            resp_coord_layout.addWidget(response_label, i//3, (i%3)*2+1)
        
        right_layout.addWidget(resp_coord_group)
        
        # 响应控制位
        resp_control_group = QGroupBox("响应控制位")
        resp_control_layout = QGridLayout(resp_control_group)
        
        response_bits = {
            25: "心跳", 26: "手动确认", 27: "自动确认", 28: "准备状态",
            29: "就绪状态", 30: "入夹确认", 31: "判定状态", 32: "判定完成",
            40: "结果NG", 41: "结果OK"
        }
        
        row = 0
        for bit, name in response_bits.items():
            resp_control_layout.addWidget(QLabel(f"{bit}位 {name}:"), row, 0)
            
            value_label = QLabel("0")
            value_label.setStyleSheet("QLabel { background-color: lightblue; padding: 3px; min-width: 60px; }")
            self.response_labels[bit] = value_label
            resp_control_layout.addWidget(value_label, row, 1)
            row += 1
        
        right_layout.addWidget(resp_control_group)
        
        # 原始数据显示
        raw_group = QGroupBox("原始数据")
        raw_layout = QVBoxLayout(raw_group)
        
        raw_layout.addWidget(QLabel("发送数据:"))
        self.send_data_label = QLabel("等待发送...")
        self.send_data_label.setStyleSheet("QLabel { background-color: lightyellow; padding: 5px; font-family: monospace; }")
        self.send_data_label.setWordWrap(True)
        raw_layout.addWidget(self.send_data_label)
        
        raw_layout.addWidget(QLabel("接收数据:"))
        self.recv_data_label = QLabel("等待接收...")
        self.recv_data_label.setStyleSheet("QLabel { background-color: lightgreen; padding: 5px; font-family: monospace; }")
        self.recv_data_label.setWordWrap(True)
        raw_layout.addWidget(self.recv_data_label)
        
        right_layout.addWidget(raw_group)
        
        # 添加到主布局
        main_layout.addLayout(left_layout)
        main_layout.addLayout(right_layout)
    
    def toggle_connection(self):
        """切换连接状态"""
        if not self.connected:
            self.connect_to_plc()
        else:
            self.disconnect_from_plc()
    
    def connect_to_plc(self):
        """连接到PLC"""
        try:
            ip = self.ip_input.text()
            port = self.port_input.value()
            
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(5.0)
            self.socket.connect((ip, port))
            
            self.connected = True
            self.connect_btn.setText("断开")
            self.send_btn.setEnabled(True)
            self.status_label.setText(f"状态: 已连接到 {ip}:{port}")
            self.status_label.setStyleSheet("QLabel { background-color: green; color: white; padding: 5px; }")
            
        except Exception as e:
            self.status_label.setText(f"连接失败: {e}")
            self.status_label.setStyleSheet("QLabel { background-color: red; color: white; padding: 5px; }")
    
    def disconnect_from_plc(self):
        """断开连接"""
        if self.socket:
            self.socket.close()
            self.socket = None
        
        self.connected = False
        self.connect_btn.setText("连接")
        self.send_btn.setEnabled(False)
        self.status_label.setText("状态: 已断开")
        self.status_label.setStyleSheet("QLabel { background-color: red; color: white; padding: 5px; }")
    
    def update_control_bit(self, bit, value):
        """更新控制位"""
        pass  # 在发送时处理
    
    def send_data(self):
        """发送数据"""
        if not self.connected:
            return
        
        try:
            # 构造坐标
            coords = []
            for input_box in self.coord_inputs:
                try:
                    coords.append(float(input_box.text()))
                except:
                    coords.append(0.0)
            
            coord_bytes = struct.pack("!6f", *coords)
            
            # 构造控制位
            control_bytes = bytearray(24)
            
            for bit, widget in self.control_bits.items():
                if isinstance(widget, QCheckBox):
                    control_bytes[bit - 24] = 255 if widget.isChecked() else 0
                else:
                    control_bytes[bit - 24] = widget.value()
            
            # 发送数据
            full_data = coord_bytes + bytes(control_bytes)
            self.socket.sendall(full_data)
            
            # 显示发送数据
            hex_str = full_data.hex()
            formatted_hex = ' '.join([hex_str[i:i+2] for i in range(0, len(hex_str), 2)])
            self.send_data_label.setText(formatted_hex)
            
            # 接收响应
            response = self.socket.recv(48)
            if len(response) == 48:
                hex_str = response.hex()
                formatted_hex = ' '.join([hex_str[i:i+2] for i in range(0, len(hex_str), 2)])
                self.recv_data_label.setText(formatted_hex)
                self.parse_response(response)
            
        except Exception as e:
            self.status_label.setText(f"通信错误: {e}")
    
    def parse_response(self, data):
        """解析响应数据"""
        # 解析坐标
        coords = struct.unpack("!6f", data[:24])
        for i, coord in enumerate(coords):
            self.coord_response_labels[i].setText(f"{coord:.3f}")
        
        # 解析控制位
        for bit in self.response_labels:
            value = data[bit - 24]
            if value == 255:
                display = "ON"
                color = "#ffebee"
            elif value > 0:
                display = f"{value}"
                color = "#fff8e1"
            else:
                display = "OFF"
                color = "#f5f5f5"
            
            self.response_labels[bit].setText(display)
            self.response_labels[bit].setStyleSheet(f"QLabel {{ background-color: {color}; padding: 3px; min-width: 60px; }}")
    
    # 快捷操作
    def clear_all(self):
        for widget in self.control_bits.values():
            if isinstance(widget, QCheckBox):
                widget.setChecked(False)
            else:
                widget.setValue(0)
    
    def set_manual(self):
        self.control_bits[26].setChecked(True)
        self.control_bits[27].setChecked(False)
    
    def set_auto(self):
        self.control_bits[26].setChecked(False)
        self.control_bits[27].setChecked(True)
    
    def set_plc_ready(self):
        self.control_bits[29].setChecked(True)
    
    def set_in_position(self):
        self.control_bits[30].setChecked(False)
        self.control_bits[31].setChecked(True)
    
    def set_judgment(self):
        self.control_bits[32].setChecked(True)
    
    def closeEvent(self, event):
        self.disconnect_from_plc()
        event.accept()

def main():
    app = QApplication(sys.argv)
    window = SimplePLCTestUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
