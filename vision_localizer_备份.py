import json, os
import pickle
import threading

import cv2
import numpy as np
import datetime

# from segmentation_rknn import SegmentationRKNN as Segmentation
from .segmentation_rknn import SegmentationRKNN as Segmentation
from .usb_camera import USBCamera
from .logger_config import logger as logging


class VisionLocalizer():
    def __init__(self, camera_config_path, tube_config_path, picture_save_path, model_path='models/model.rknn'):
        self._lock = threading.RLock()
        # self.tube_config_path = tube_config_path
        # self.camera_config_path = camera_config_path
        self._load_camera_config(camera_config_path)
        self._load_tube_config(tube_config_path)
        # self.read_localizer_config(camera_config_path, tube_config_path)
        self.cap = USBCamera()
        self.segmentation_model = Segmentation(model_path=model_path)  # 'models/model.rknn'
        self.picture_save_path = picture_save_path
        if not os.path.isdir(picture_save_path):
            os.makedirs(picture_save_path)

    def update_tube_config(self, tube_config_path):
        """运行时动态刷新管件配置，不重新读相机参数"""
        with self._lock:
            path = r"./configs/tube_config_6.pkl"
            self._load_tube_config(path)

    def _load_camera_config(self, camera_config_path):
        with open(camera_config_path, "rb") as f:
            camera_params = pickle.load(f)
        self.mtxL = camera_params['mtxL']
        self.fx, self.fy = self.mtxL[0, 0], self.mtxL[1, 1]
        self.cx, self.cy = self.mtxL[0, 2], self.mtxL[1, 2]
        self.T = camera_params['T']
        self.baseline = np.linalg.norm(self.T)
        self.Left_Stereo_Map = camera_params['Left_Stereo_Map']
        self.Right_Stereo_Map = camera_params['Right_Stereo_Map']
        logging.info(f"Camera params loaded from: {camera_config_path}")

    def _load_tube_config(self, tube_config_path):
        print("--------")
        print(f"========{tube_config_path}")
        with open(tube_config_path, "rb") as f:
            tube_params = pickle.load(f)
        self.tube_left_cy = tube_params['left_cy']
        self.tube_point3d = tube_params['point3d']
        print("当前 tube_point3d:", self.tube_point3d)
        logging.info(f"Tube config loaded from: {tube_config_path}")

    def connect_camera(self):
        self.cap.connect_camera()
        if self.camera_is_connected() == 0:
            self.configure_camera()

    def camera_is_connected(self):
        return self.cap.camera_is_connected()

    def configure_camera(self):
        self.cap.configure_camera()

    def release_camera(self):
        self.cap.release_camera()

    def capture_image(self, **options):
        mode = options.get('mode', 'prod')
        frameL_path = options.get('left_path', None)
        frameR_path = options.get('right_path', None)

        if mode == 'test':
            if None in [frameL_path, frameR_path]:
                logging.warning("Test mode requires both left and right image paths.")
                return None
            else:
                frameL = cv2.imdecode(np.fromfile(frameL_path, dtype=np.uint8), cv2.IMREAD_COLOR)
                frameR = cv2.imdecode(np.fromfile(frameR_path, dtype=np.uint8), cv2.IMREAD_COLOR)
                return [frameL, frameR]
        else:
            frames = self.cap.capture()
            if frames is None:
                logging.error("Failed to capture frames from camera.")
            return frames

    def image_segmentation_process(self, img, **options):
        mode = options.get('mode', 'prod')
        result = self.segmentation_model.main_point_localization(img=img, mode=mode, cy=self.tube_left_cy)

        return result

    def get_current_frame(self):
        return self.cap.get_latest_frame()

    def rectify_image(self, img, stereo_map):
        rectified_image = cv2.remap(img, stereo_map[0], stereo_map[1], interpolation=cv2.INTER_LANCZOS4,
                                    borderMode=cv2.BORDER_CONSTANT)
        return rectified_image

    def calc_3d_point(self, x, y, disp_value):
        X_3D, Y_3D, Z = None, None, None

        # 计算深度 Z
        if disp_value > 0:  # 避免除零
            Z = (self.fx * self.baseline) / disp_value
            # 计算3D坐标
            X_3D = (x - self.cx) * Z / self.fx
            Y_3D = (y - self.cy) * Z / self.fy

        return [X_3D, Y_3D, Z]

    def main_rectify_poses(self, **options):
        logging.info("Start poses rectify process")
        save = options.get('save', False)
        mode = options.get('mode', 'prod')
        frameL_path = options.get('left_path', None)
        frameR_path = options.get('right_path', None)
        image_name = options.get('image_name', None)
        test_save_folder = options.get('test_save_folder', 'test_img_result')

        logging.debug(
            f"Getting process options:\n  save picture: {save}\n  run mode:{mode}\n  local image name: {image_name}\n  local left image: {frameL_path}\n  local right image: {frameR_path} \n  save path for test mode: {test_save_folder}")

        if save:
            timestr = datetime.datetime.now().strftime("%Y-%m-%d_%H_%M_%S_%f")[:-3]
            logging.debug(f"Creating timestamp: {timestr}")
            out_folder_path = os.path.join(self.picture_save_path, timestr)
            os.makedirs(out_folder_path)

        # success, pose.x, pose.y, 999.999, 999.999, 999.999, 999.999
        rectify_result = [-1, 9999.999, 9999.999, 9999.999, 9999.999, 9999.999, 9999.999]

        if mode == 'test':
            logging.info("Loading images from local file...")
            frames = self.capture_image(mode=mode, left_path=frameL_path, right_path=frameR_path)
        else:
            logging.info("Loading images from camera capture...")
            frames = self.capture_image()
        if frames is None:
            logging.info("Failed to load images.")
        else:
            # return rectify_result
            logging.info("Images loaded successfully. ")

            frameL, frameR = frames
            if save:
                logging.info(f"Saving images to: {out_folder_path}")
                cv2.imencode('.png', frameL)[1].tofile(os.path.join(out_folder_path, f'{timestr}_L.png'))
                cv2.imencode('.png', frameR)[1].tofile(os.path.join(out_folder_path, f'{timestr}_R.png'))

            logging.info("Rectifying images by stereo map... ")
            frameL = self.rectify_image(frameL, self.Left_Stereo_Map)
            frameR = self.rectify_image(frameR, self.Right_Stereo_Map)
            logging.info("Images rectified. ")
            if save:
                logging.info(f"Saving rectified images to: {out_folder_path}")
                cv2.imencode('.png', frameL)[1].tofile(os.path.join(out_folder_path, f'{timestr}_LRect.png'))
                cv2.imencode('.png', frameR)[1].tofile(os.path.join(out_folder_path, f'{timestr}_RRect.png'))

            logging.info("Start image segmentation (LEFT) process...")
            _, left_cx, left_cy, left_tube_contour, left_img_vis = self.image_segmentation_process(frameL)
            logging.debug(f"Image segmentation (LEFT) result:\n  left_cx = {left_cx}\n  left_cy = {left_cy}")
            logging.info("Start image segmentation (RIGHT) process...")
            _, right_cx, right_cy, right_tube_contour, right_img_vis = self.image_segmentation_process(frameR)
            logging.debug(f"Image segmentation (RIGHT) result:\n  right_cx = {right_cx}\n  right_cy = {right_cy}")

            if not None in [left_cx, left_cy, right_cx, right_cy]:
                logging.info("Start calculating current 3D point...")
                disp_value = left_cx - right_cx
                logging.debug(
                    f"Getting disparity value:\n  left_cx {left_cx} - right_cx {right_cx} = Disparity {disp_value}")
                current_point3d = self.calc_3d_point(left_cx, left_cy, disp_value)
                logging.debug(f"Getting 3D point:{current_point3d}")
                if not None in current_point3d:
                    logging.info("Start calculating 3D traslation...")
                    logging.debug(
                        f"Getting calibrated 3D point:\n  X:{float(self.tube_point3d[0])}\n  Z:{float(self.tube_point3d[2])}")
                    logging.debug(
                        f"Getting current 3D point:\n  X:{float(current_point3d[0])}\n  Z:{float(current_point3d[2])}")

                    X_diff = self.tube_point3d[0] - current_point3d[0]
                    Z_diff = self.tube_point3d[2] - current_point3d[2]

                    logging.debug(f"Getting X deviation (calibrated_X - current_X): {X_diff}")
                    logging.debug(f"Getting Z deviation (calibrated_Z - current_Z): {Z_diff}")

                    rectify_result[0] = 0
                    rectify_result[1] = round(X_diff, 3)
                    rectify_result[2] = round(Z_diff, 3)
                else:
                    logging.info(
                        "Cannot calculate 3D traslation, because some element of current_point3d is None. Exit")
            else:
                logging.info("Cannot calculate 3D point, because left_cx or right_cx is None. Exit")

            if save:
                logging.info(f"Saving segmented left image to: {out_folder_path}")
                cv2.imencode('.png', left_img_vis)[1].tofile(os.path.join(out_folder_path, f'{timestr}_LRect_segm.png'))
                cv2.imencode('.png', right_img_vis)[1].tofile(
                    os.path.join(out_folder_path, f'{timestr}_RRect_segm.png'))

                save_txt_path = os.path.join(out_folder_path, f'{timestr}_rectify_result.txt')
                logging.info(f"Writing rectify_result to path: {save_txt_path}")
                with open(save_txt_path, 'w') as f:
                    text = f'{rectify_result}'
                    f.write(text)

            if mode == 'test':
                if not os.path.isdir(test_save_folder):
                    os.makedirs(test_save_folder)
                left_out_path = os.path.join(test_save_folder, f'{image_name}_L_result.png')
                right_out_path = os.path.join(test_save_folder, f'{image_name}_R_result.png')
                cv2.imencode('.png', left_img_vis)[1].tofile(left_out_path)
                cv2.imencode('.png', right_img_vis)[1].tofile(right_out_path)
                with open(os.path.join(test_save_folder, f'{image_name}_rectify_result.txt'), 'w') as f:
                    text = f'{rectify_result}'
                    f.write(text)

        return rectify_result
