#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PLC信号测试UI
模拟PLC的所有信号，实时显示返回数据
"""

import sys
import socket
import struct
import threading
import time
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QGridLayout, QWidget, QPushButton, QLabel, QCheckBox, 
                             QLineEdit, QTextEdit, QGroupBox, QSpinBox, QComboBox,
                             QScrollArea, QFrame, QSplitter)
from PyQt5.QtCore import QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QColor, QPalette

class PLCTestUI(QMainWindow):
    """PLC测试UI主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PLC信号测试工具")
        self.setGeometry(100, 100, 1400, 900)
        
        # 连接状态
        self.connected = False
        self.socket = None
        
        # 数据存储
        self.send_data = bytearray(48)  # 发送的48字节数据
        self.recv_data = bytearray(48)  # 接收的48字节数据
        
        # 控制位状态
        self.control_bits = {}
        self.response_bits = {}
        
        self.init_ui()
        self.init_timer()
        
    def init_ui(self):
        """初始化UI界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter()
        main_layout.addWidget(splitter)
        
        # 左侧：发送控制面板
        left_widget = self.create_send_panel()
        splitter.addWidget(left_widget)
        
        # 右侧：接收显示面板
        right_widget = self.create_receive_panel()
        splitter.addWidget(right_widget)
        
        # 设置分割比例
        splitter.setSizes([700, 700])
        
    def create_send_panel(self):
        """创建发送控制面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 连接控制
        conn_group = QGroupBox("连接控制")
        conn_layout = QHBoxLayout(conn_group)
        
        self.ip_input = QLineEdit("127.0.0.1")
        self.port_input = QSpinBox()
        self.port_input.setRange(1, 65535)
        self.port_input.setValue(2000)
        
        self.connect_btn = QPushButton("连接")
        self.connect_btn.clicked.connect(self.toggle_connection)
        
        conn_layout.addWidget(QLabel("IP:"))
        conn_layout.addWidget(self.ip_input)
        conn_layout.addWidget(QLabel("端口:"))
        conn_layout.addWidget(self.port_input)
        conn_layout.addWidget(self.connect_btn)
        
        layout.addWidget(conn_group)
        
        # 坐标控制
        coord_group = QGroupBox("坐标数据 (前24字节)")
        coord_layout = QGridLayout(coord_group)
        
        self.coord_inputs = []
        coord_labels = ["X", "Y", "Z", "RX", "RY", "RZ"]
        for i, label in enumerate(coord_labels):
            coord_layout.addWidget(QLabel(f"{label}:"), i//3, (i%3)*2)
            input_box = QLineEdit("0.0")
            self.coord_inputs.append(input_box)
            coord_layout.addWidget(input_box, i//3, (i%3)*2+1)
        
        layout.addWidget(coord_group)
        
        # 控制位设置
        control_group = QGroupBox("控制位设置 (25-42位)")
        control_layout = QVBoxLayout(control_group)

        control_scroll = QScrollArea()
        control_widget = QWidget()
        control_grid = QGridLayout(control_widget)

        # 定义控制位
        control_bits_def = {
            25: ("心跳", "spinbox", "心跳计数器，用于检测连接状态"),
            26: ("手动模式", "checkbox", "255=启动手动模式"),
            27: ("自动模式", "checkbox", "255=启动自动模式"),
            28: ("准备请求", "spinbox", "特殊值：9=准备中"),
            29: ("PLC准备入夹", "checkbox", "255=PLC准备开始入夹"),
            30: ("未到位", "checkbox", "255=PLC未到位"),
            31: ("到位", "checkbox", "255=PLC已到位"),
            32: ("开始判定", "checkbox", "255=开始视觉判定"),
            33: ("松夹", "checkbox", "255=松开夹爪"),
            34: ("夹紧", "checkbox", "255=夹紧夹爪"),
            35: ("判定开始", "checkbox", "255=判定流程开始"),
            36: ("结果确认", "checkbox", "255=确认收到判定结果"),
            37: ("预留37", "checkbox", "预留控制位"),
            38: ("预留38", "checkbox", "预留控制位"),
            39: ("预留39", "checkbox", "预留控制位"),
            40: ("预留40", "checkbox", "预留控制位"),
            41: ("预留41", "checkbox", "预留控制位"),
            42: ("预留42", "checkbox", "预留控制位"),
        }
        
        row = 0
        for bit, (name, widget_type, description) in control_bits_def.items():
            # 位号和名称
            control_grid.addWidget(QLabel(f"{bit}位"), row, 0)
            control_grid.addWidget(QLabel(name), row, 1)

            # 控件
            if widget_type == "checkbox":
                widget = QCheckBox()
                widget.stateChanged.connect(lambda state, b=bit: self.update_control_bit(b, state == 2))
            else:  # spinbox
                widget = QSpinBox()
                widget.setRange(0, 255)
                widget.valueChanged.connect(lambda value, b=bit: self.update_control_bit(b, value))

            self.control_bits[bit] = widget
            control_grid.addWidget(widget, row, 2)

            # 说明
            desc_label = QLabel(description)
            desc_label.setStyleSheet("QLabel { color: gray; font-size: 10px; }")
            control_grid.addWidget(desc_label, row, 3)

            row += 1

        control_scroll.setWidget(control_widget)
        control_scroll.setWidgetResizable(True)
        control_scroll.setMaximumHeight(300)

        control_layout.addWidget(control_scroll)

        layout.addWidget(control_group)

        # 快捷操作按钮
        shortcut_group = QGroupBox("快捷操作")
        shortcut_layout = QGridLayout(shortcut_group)

        # 创建快捷按钮
        shortcuts = [
            ("清空所有", self.clear_all_bits),
            ("启动手动模式", self.set_manual_mode),
            ("启动自动模式", self.set_auto_mode),
            ("PLC准备入夹", self.set_plc_ready),
            ("PLC到位", self.set_plc_in_position),
            ("开始判定", self.set_start_judgment),
            ("确认结果", self.set_result_received),
            ("心跳+1", self.increment_heartbeat),
        ]

        for i, (text, func) in enumerate(shortcuts):
            btn = QPushButton(text)
            btn.clicked.connect(func)
            shortcut_layout.addWidget(btn, i//4, i%4)

        layout.addWidget(shortcut_group)

        # 发送按钮
        send_layout = QHBoxLayout()
        self.send_btn = QPushButton("发送数据")
        self.send_btn.clicked.connect(self.send_data_to_plc)
        self.send_btn.setEnabled(False)
        self.send_btn.setStyleSheet("QPushButton { background-color: lightblue; font-weight: bold; padding: 10px; }")

        self.auto_send_checkbox = QCheckBox("自动发送")
        self.auto_send_checkbox.stateChanged.connect(self.toggle_auto_send)

        send_layout.addWidget(self.send_btn)
        send_layout.addWidget(self.auto_send_checkbox)
        layout.addLayout(send_layout)
        
        return widget
    
    def create_receive_panel(self):
        """创建接收显示面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 连接状态显示
        self.status_label = QLabel("状态: 未连接")
        self.status_label.setStyleSheet("QLabel { background-color: red; color: white; padding: 5px; }")
        layout.addWidget(self.status_label)
        
        # 响应坐标显示
        coord_resp_group = QGroupBox("响应坐标数据")
        coord_resp_layout = QGridLayout(coord_resp_group)
        
        self.coord_response_labels = []
        coord_labels = ["X", "Y", "Z", "RX", "RY", "RZ"]
        for i, label in enumerate(coord_labels):
            coord_resp_layout.addWidget(QLabel(f"{label}:"), i//3, (i%3)*2)
            response_label = QLabel("0.0")
            response_label.setStyleSheet("QLabel { background-color: lightgray; padding: 3px; }")
            self.coord_response_labels.append(response_label)
            coord_resp_layout.addWidget(response_label, i//3, (i%3)*2+1)
        
        layout.addWidget(coord_resp_group)
        
        # 响应控制位显示
        response_group = QGroupBox("响应控制位 (25-42位)")
        response_layout = QGridLayout(response_group)
        
        # 定义响应位
        response_bits_def = {
            25: "心跳",
            26: "手动模式确认",
            27: "自动模式确认", 
            28: "准备状态",
            29: "就绪状态",
            30: "入夹确认",
            31: "判定状态",
            32: "判定完成",
            33: "正在夹紧",
            34: "已夹紧",
            35: "正在松夹",
            36: "已松夹",
            37: "工作状态",
            38: "松夹反馈",
            39: "夹紧反馈",
            40: "结果NG",
            41: "结果OK",
            42: "夹爪异常",
        }
        
        row = 0
        for bit, name in response_bits_def.items():
            response_layout.addWidget(QLabel(f"{bit}位 {name}:"), row, 0)
            
            value_label = QLabel("0")
            value_label.setStyleSheet("QLabel { background-color: lightblue; padding: 3px; min-width: 40px; }")
            self.response_bits[bit] = value_label
            response_layout.addWidget(value_label, row, 1)
            
            # 添加状态指示器
            status_label = QLabel("●")
            status_label.setStyleSheet("QLabel { color: gray; font-size: 16px; }")
            response_layout.addWidget(status_label, row, 2)
            self.response_bits[f"{bit}_status"] = status_label
            
            row += 1
        
        layout.addWidget(response_group)
        
        # 原始数据显示
        raw_group = QGroupBox("原始数据")
        raw_layout = QVBoxLayout(raw_group)

        # 发送数据
        raw_layout.addWidget(QLabel("发送数据 (48字节):"))
        self.send_data_label = QLabel("等待发送...")
        self.send_data_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                padding: 8px;
                font-family: 'Courier New', monospace;
                font-size: 11px;
                border-radius: 4px;
            }
        """)
        self.send_data_label.setWordWrap(True)
        raw_layout.addWidget(self.send_data_label)

        # 接收数据
        raw_layout.addWidget(QLabel("接收数据 (48字节):"))
        self.recv_data_label = QLabel("等待接收...")
        self.recv_data_label.setStyleSheet("""
            QLabel {
                background-color: #d4edda;
                border: 1px solid #c3e6cb;
                padding: 8px;
                font-family: 'Courier New', monospace;
                font-size: 11px;
                border-radius: 4px;
            }
        """)
        self.recv_data_label.setWordWrap(True)
        raw_layout.addWidget(self.recv_data_label)

        # 数据解析显示
        parse_layout = QHBoxLayout()

        # 坐标部分
        coord_parse_label = QLabel("坐标部分 (0-23):")
        self.coord_parse_display = QLabel("等待数据...")
        self.coord_parse_display.setStyleSheet("QLabel { background-color: #e3f2fd; padding: 5px; font-family: monospace; }")

        # 控制部分
        control_parse_label = QLabel("控制部分 (24-47):")
        self.control_parse_display = QLabel("等待数据...")
        self.control_parse_display.setStyleSheet("QLabel { background-color: #fce4ec; padding: 5px; font-family: monospace; }")

        parse_left = QVBoxLayout()
        parse_left.addWidget(coord_parse_label)
        parse_left.addWidget(self.coord_parse_display)

        parse_right = QVBoxLayout()
        parse_right.addWidget(control_parse_label)
        parse_right.addWidget(self.control_parse_display)

        parse_layout.addLayout(parse_left)
        parse_layout.addLayout(parse_right)
        raw_layout.addLayout(parse_layout)

        layout.addWidget(raw_group)
        
        return widget
    
    def init_timer(self):
        """初始化定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_display)
        self.timer.start(100)  # 100ms更新一次
    
    def toggle_connection(self):
        """切换连接状态"""
        if not self.connected:
            self.connect_to_plc()
        else:
            self.disconnect_from_plc()
    
    def connect_to_plc(self):
        """连接到PLC"""
        try:
            ip = self.ip_input.text()
            port = self.port_input.value()
            
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(5.0)
            self.socket.connect((ip, port))
            
            self.connected = True
            self.connect_btn.setText("断开")
            self.send_btn.setEnabled(True)
            self.status_label.setText(f"状态: 已连接到 {ip}:{port}")
            self.status_label.setStyleSheet("QLabel { background-color: green; color: white; padding: 5px; }")
            
        except Exception as e:
            self.status_label.setText(f"连接失败: {e}")
            self.status_label.setStyleSheet("QLabel { background-color: red; color: white; padding: 5px; }")
    
    def disconnect_from_plc(self):
        """断开PLC连接"""
        if self.socket:
            self.socket.close()
            self.socket = None
        
        self.connected = False
        self.connect_btn.setText("连接")
        self.send_btn.setEnabled(False)
        self.status_label.setText("状态: 已断开")
        self.status_label.setStyleSheet("QLabel { background-color: red; color: white; padding: 5px; }")
    
    def update_control_bit(self, bit, value):
        """更新控制位值"""
        if bit == 25:  # 心跳位
            self.send_data[bit - 24] = value
        else:
            self.send_data[bit - 24] = 255 if value else 0

    # 快捷操作函数
    def clear_all_bits(self):
        """清空所有控制位"""
        for bit, widget in self.control_bits.items():
            if isinstance(widget, QCheckBox):
                widget.setChecked(False)
            else:
                widget.setValue(0)

    def set_manual_mode(self):
        """设置手动模式"""
        self.control_bits[26].setChecked(True)  # 手动模式
        self.control_bits[27].setChecked(False)  # 关闭自动模式

    def set_auto_mode(self):
        """设置自动模式"""
        self.control_bits[26].setChecked(False)  # 关闭手动模式
        self.control_bits[27].setChecked(True)   # 自动模式

    def set_plc_ready(self):
        """设置PLC准备入夹"""
        self.control_bits[29].setChecked(True)

    def set_plc_in_position(self):
        """设置PLC到位"""
        self.control_bits[30].setChecked(False)  # 关闭未到位
        self.control_bits[31].setChecked(True)   # 设置到位

    def set_start_judgment(self):
        """设置开始判定"""
        self.control_bits[32].setChecked(True)

    def set_result_received(self):
        """设置结果确认"""
        self.control_bits[36].setChecked(True)

    def increment_heartbeat(self):
        """心跳+1"""
        current = self.control_bits[25].value()
        self.control_bits[25].setValue((current + 1) % 256)

    def toggle_auto_send(self, state):
        """切换自动发送模式"""
        if state == 2:  # 选中
            self.auto_timer = QTimer()
            self.auto_timer.timeout.connect(self.send_data_to_plc)
            self.auto_timer.start(1000)  # 每秒发送一次
        else:
            if hasattr(self, 'auto_timer'):
                self.auto_timer.stop()
    
    def send_data_to_plc(self):
        """发送数据到PLC"""
        if not self.connected or not self.socket:
            return
        
        try:
            # 构造坐标数据
            coords = []
            for input_box in self.coord_inputs:
                try:
                    coords.append(float(input_box.text()))
                except:
                    coords.append(0.0)
            
            # 打包坐标数据
            coord_bytes = struct.pack("!6f", *coords)
            
            # 构造完整的48字节数据
            full_data = coord_bytes + bytes(self.send_data[24:])
            
            # 发送数据
            self.socket.sendall(full_data)
            
            # 更新发送数据显示
            hex_str = full_data.hex()
            formatted_hex = ' '.join([hex_str[i:i+2] for i in range(0, len(hex_str), 2)])
            self.send_data_label.setText(formatted_hex)

            # 更新发送数据解析显示
            coord_hex = ' '.join([hex_str[i:i+2] for i in range(0, 48, 2)])
            control_hex = ' '.join([hex_str[i:i+2] for i in range(48, 96, 2)])
            self.coord_parse_display.setText(coord_hex)
            self.control_parse_display.setText(control_hex)

            # 接收响应
            response = self.socket.recv(48)
            if len(response) == 48:
                self.recv_data = bytearray(response)
                hex_str = response.hex()
                formatted_hex = ' '.join([hex_str[i:i+2] for i in range(0, len(hex_str), 2)])
                self.recv_data_label.setText(formatted_hex)
                self.parse_response_data()
            
        except Exception as e:
            self.status_label.setText(f"通信错误: {e}")
            self.status_label.setStyleSheet("QLabel { background-color: orange; color: white; padding: 5px; }")
    
    def parse_response_data(self):
        """解析响应数据"""
        if len(self.recv_data) < 48:
            return

        # 解析坐标
        coords = struct.unpack("!6f", self.recv_data[:24])
        for i, coord in enumerate(coords):
            self.coord_response_labels[i].setText(f"{coord:.3f}")

        # 解析控制位
        for bit in range(25, 43):
            if bit in self.response_bits:
                value = self.recv_data[bit - 24]

                # 格式化显示值
                if bit == 25:  # 心跳
                    display_text = str(value)
                elif bit in [28, 29, 30, 31, 32]:  # 特殊状态码
                    if value == 0:
                        display_text = "0"
                    elif value == 255:
                        display_text = "255 (ON)"
                    else:
                        display_text = f"{value} (状态码)"
                else:  # 普通开关量
                    display_text = "ON" if value == 255 else "OFF" if value == 0 else str(value)

                self.response_bits[bit].setText(display_text)

                # 更新状态指示器和背景色
                status_key = f"{bit}_status"
                if status_key in self.response_bits:
                    if value == 255:
                        color = "#e74c3c"  # 红色 - 激活
                        bg_color = "#ffebee"
                    elif value > 0:
                        color = "#f39c12"  # 橙色 - 特殊值
                        bg_color = "#fff8e1"
                    else:
                        color = "#95a5a6"  # 灰色 - 未激活
                        bg_color = "#f5f5f5"

                    self.response_bits[status_key].setStyleSheet(f"QLabel {{ color: {color}; font-size: 16px; }}")
                    self.response_bits[bit].setStyleSheet(f"QLabel {{ background-color: {bg_color}; padding: 3px; min-width: 60px; border-radius: 3px; }}")
    
    def update_display(self):
        """更新显示"""
        # 这里可以添加定期更新的逻辑
        pass
    
    def closeEvent(self, event):
        """关闭事件"""
        self.disconnect_from_plc()
        event.accept()

def main():
    app = QApplication(sys.argv)
    window = PLCTestUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
