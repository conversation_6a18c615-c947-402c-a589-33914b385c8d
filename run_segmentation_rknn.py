import cv2
import json
import struct
import threading
import time
from datetime import datetime
from server_utils.vision_localizer import VisionLocalizer
import os
import glob
import numpy as np

from server_utils.logger_config import logger as logging                    
from server_utils.segmentation_rknn import SegmentationRKNN as Segmentation

def run_segmentation_main(image_root,result_root, model_path):
	
    rknn_model = Segmentation(model_path=model_path)  # 'models/model.rknn'
        
    for img_path in glob.glob(os.path.join(image_root, '*.png')):
        img_name = os.path.basename(img_path)
        image = cv2.imdecode(np.fromfile(img_path, dtype=np.uint8), cv2.IMREAD_COLOR)

        start_time = time.time()
        origin, img = rknn_model.preprocess(image)
        outputs = rknn_model.infer(img)
        _, cx, cy, _, img_vis = rknn_model.run_image_post_process(origin, outputs,cy=300)
        duration = time.time() - start_time
        print('pred duration:',duration)
        new_path = os.path.join(result_root, img_name)
        cv2.imencode('.png', img_vis)[1].tofile(new_path)



    #rknn_model.release_model()
    

if __name__ == "__main__":
    image_folder = '/home/<USER>/Desktop/orangepi_server/换夹香橙派测试/测试图片/20250528现场反馈/20250528整合/'
    hrnet_result_folder = image_folder + '_hrnet'
    ocrnet_result_folder = image_folder + '_ocrnet'
    os.makedirs(hrnet_result_folder, exist_ok=True)
    os.makedirs(ocrnet_result_folder, exist_ok=True)
    hrnet_model_path = '/home/<USER>/Desktop/orangepi_server/换夹香橙派测试/rknn模型/hrnet_model_opt0_1.rknn'
    ocrnet_model_path = '/home/<USER>/Desktop/orangepi_server/换夹香橙派测试/rknn模型/changeclip_tube_seg_ocrnet_20250528_model.rknn'
    
    # run hrnet
    run_segmentation_main(image_folder,hrnet_result_folder, hrnet_model_path)

    # run ocrnet
    run_segmentation_main(image_folder,ocrnet_result_folder, ocrnet_model_path)
